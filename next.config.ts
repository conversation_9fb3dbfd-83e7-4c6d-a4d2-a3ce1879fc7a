import type { NextConfig } from "next";

const nextConfig: NextConfig = {
    /* config options here */
    reactStrictMode: false,
    images: {
        remotePatterns: [
            {
                protocol: "https",
                hostname: "www.ikea.com",
                pathname: "/de/en/images/products/**",
            },
            {
                protocol: "https",
                hostname: "files.virgool.io",
                pathname: "/upload/**",
            },
            {
                protocol: "https",
                hostname: "us.e-cloth.com",
                pathname: "/**",
            },
            {
                protocol: "https",
                hostname: "satvikworld.com",
                pathname: "/**",
            },
            {
                protocol: "https",
                hostname: "www.satvikworld.com",
                pathname: "/**",
            },
            {
                protocol: "https",
                hostname: "khodrox.iranisoft.ir",
                pathname: "/**",
            },
            {
                protocol: "https",
                hostname: "picsum.photos",
                pathname: "/**",
            }, {
                protocol: "https",
                hostname: "shop-khodrox.liara.run",
                pathname: "/**",
            },

        ],
        // domains: ['www.ikea.com','www.us.e-cloth.com','satvikworld.com'],
        domains: ['localhost', 'dkstatics-public.digikala.com', 'dl.khodrox.com'],
    },
    eslint: {
        // Warning: This allows production builds to successfully complete even if
        // your project has ESLint errors.
        ignoreDuringBuilds: true,
    },
};

export default nextConfig;
