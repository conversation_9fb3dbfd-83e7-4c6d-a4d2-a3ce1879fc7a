export type Category = {
    title: string;
    slug: string;
    children?: Category[];
}

export interface CategorySearchableAttributes {
    title: string;
    english_title: 'color' | 'size' | 'brand';
    values: { title: string; value: string }[];
}


export interface CategoryFilterValue {
  title: string;
  value: string;
}

export interface CategoryFilter {
  title: string;
  english_title: string;
  values: CategoryFilterValue[];
}