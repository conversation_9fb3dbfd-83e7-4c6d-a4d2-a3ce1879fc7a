import {z} from "zod";
import {IRAN_MOBILE_REGEX} from "@/lib/regex";

export enum ViolationTypeEnum {
    WITH_INFO = "withInfo",
    WITHOUT_INFO = "withoutInfo",
}

export const violationInquiryFormSchema = z.object({
    plateNumber: z
        .array(z.string()),
    type: z.enum([ViolationTypeEnum.WITH_INFO, ViolationTypeEnum.WITHOUT_INFO]), // Type must be one of the two allowed values.
    nationalCode: z.string().optional(),
    phoneNumber: z.string()
        .regex(IRAN_MOBILE_REGEX, "لطفا موبایل را صحیح وارد کنید")
        .optional(),
}).superRefine((data, ctx) => {
    if (data.type === ViolationTypeEnum.WITH_INFO) {
        if (!data.nationalCode) {
            ctx.addIssue({
                path: ["nationalCode"],
                message: "کد ملی الزامی است",
                code: z.ZodIssueCode.custom,
            });
        }
        if (!data.phoneNumber) {
            ctx.addIssue({
                path: ["phoneNumber"],
                message: "شماره موبایل الزامی است",
                code: z.ZodIssueCode.custom,
            });
        }
    }
});

export type ViolationInquiryType = z.infer<typeof violationInquiryFormSchema>

export const DrivingLicensePointFormSchema = z.object({
    nationalCode: z.string(),
    drivingLicense: z.string(),
    phoneNumber: z.string()
        .regex(IRAN_MOBILE_REGEX, "لطفا موبایل را صحیح وارد کنید")
        .optional(),
})

export type DrivingLicensePointType = z.infer<typeof DrivingLicensePointFormSchema>

export const InsuranceInquiryFormSchema = z.object({
    plateNumber: z
        .array(z.string()),
    type: z.enum([ViolationTypeEnum.WITH_INFO, ViolationTypeEnum.WITHOUT_INFO]), // Type must be one of the two allowed values.
    nationalCode: z.string(),
    insuranceNumber: z.string(),
}).superRefine((data, ctx) => {
    if (data.type === ViolationTypeEnum.WITH_INFO) {
        if (!data.nationalCode) {
            ctx.addIssue({
                path: ["nationalCode"],
                message: "کد ملی الزامی است",
                code: z.ZodIssueCode.custom,
            });
        }
    }
});

export type InsuranceInquiryType = z.infer<typeof InsuranceInquiryFormSchema>


export const loginFormSchema = z.object({
    mobile: z
        .string()
        .regex(IRAN_MOBILE_REGEX, "لطفا شماره صحیح را وارد کنید"),
    code: z.string().length(5, "کد تایید صحیح نمی باشد"),
});
export type LoginFormType = z.infer<typeof loginFormSchema>;
