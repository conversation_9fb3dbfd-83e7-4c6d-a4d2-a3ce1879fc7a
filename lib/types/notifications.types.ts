export interface NotificationsResponse {
  data: NotificationsData
}
export interface NotificationsData {  
    data: NotificationItem[];
    pagination: Pagination; 
}

export interface NotificationItem {
  id: number;
  title: string;
  body: string;
  is_read: boolean;
  data: NotificationData;
  read_at: string | null;
  created_at: string;
  created_date_ago: string;
  pagination: Pagination;
}

export type NotificationData =
  | InvoiceCreatedData
  | PaymentSuccessData
  | WalletTopupData;

export interface InvoiceCreatedData {
  type: "invoice_created";
  invoice_id: number;
  invoice_number: string;
  total: number;
}

export interface PaymentSuccessData {
  type: "payment_success";
  invoice_id: number;
  transaction_id: number;
  amount: number;
  payment_method: string;
}

export interface WalletTopupData {
  type: "wallet_topup";
  transaction_id: number;
  amount: number;
}

export interface Pagination {
  total: number;
  current_page: number;
  last_page: number;
  per_page: number;
}
