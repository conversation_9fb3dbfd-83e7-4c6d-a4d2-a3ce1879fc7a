export interface ProductResponse {
    success: boolean;
    message: string;
    data: ProductData;
    status: number;
}

export interface ProductData {
    id: string
    title: string;
    slug: string;
    description: string;
    comments_count: number;
    questions_count: number;
    product_rates_count: number;
    product_rate: number | null;
    meta: Meta;
    galleries: Gallery[];
    details: Detail[];
    variants: Variation[];
    default_variant: Variation;
    attributes: Attribute[];
    sizes: string[];
    colors: string[];
    guarantees: Guarantee[];
    delivery_methods: any[]; // Assuming empty array or define properly if you get data later
    shop: Shop;
    is_favorite: true
    category: {
        title: string
        slug: string
    };
}

export interface Meta {
    title: string;
    description: string;
    keywords: string;
}

export interface Gallery {
    url: string;
    caption: string;
}

export interface Detail {
    key: string;
    value: string;
}

export interface Variation {
    id: string;
    attributes: SelectedVariant[];
    sku: string;
    price: number;
    sale_price: number
    current_quantity: number;
    size: string;
    color: string;
}

export interface SelectedVariant {
    title: string;
    value: string | number;
    extra: {
        [key: string]: string | number | undefined;
    } | null;

}

export interface Attribute {
    type: string;
    title: string;
    values: AttributeValue[];
}

export interface AttributeValue {
    value: string;
    title: string;
    extra_data?: ExtraData;
}

export interface ExtraData {
    hex: string;
}

export interface Guarantee {
    id: string;
    price: number;
    months: number;
    company_name: string;
}

export interface Shop {
    id: string;
    title: string;
}

interface ProductHelpData {
    id: string;
    title: string;
    order: number;
    content: string; // contains HTML string
}

export interface ProductHelpResponse {
    success: boolean;
    message: string;
    data: ProductHelpData[];
    status: number;
}

export interface ProductCommentsResponse {
    success: boolean;
    message: string;
    status: number;
    data: ProductComment[];
}

export interface ProductComment {
    id: string;
    body: string;
    rate: number;
    has_bought: boolean;
    created_at: string; // ISO date string
    user: CommentUser;
    status: string;
    replies?: CommentReply[];
}


export interface CommentUser {
    id: string;
    full_name: string;
}

export interface CommentReply {
    id: string;
    body: string;
    rate: number | null;
    has_bought: boolean | null;
    created_at: string; // ISO date string
}

export interface User {
    id: string;
    name: string;
}

export interface Answer {
    id: string;
    body: string;
    created_at: string;
    user: User;
}

export interface Question {
    id: string;
    body: string;
    created_at: string;
    user: User;
    answers: Answer[];
}

export interface ProductQuestionsResponse {
    success: boolean;
    message: string;
    data: Question[];
    status: number;
}

export interface SimilarProductsResponse {
    success: boolean;
    message: string;
    data: SimilarProduct[];
    status: number;
}

export interface SimilarProduct {
    title: string;
    slug: string;
    image: {
        url: string;
        caption: string;
    };
    rate: number;
    price: number;
    actualy_price: number;
    in_stock: boolean;
    sale_price: number | null;
    product_id: string;
}

export interface CartItemAttribute {
    type: string;
    title: string;
    value: string;
    extra_data: {
        [key: string]: any;
    } | null;
}

export interface CartItem {
    product_id: string;
    id: string;
    name: string;
    sku: string;
    price: number;
    quantity: number;
    total: number;
    image: string;
    attributes: CartItemAttribute[];
}

export type Pagination = {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
};

export type ProductResponseData = {
    pagination: Pagination;
    total_products: number;
    min_price: number;
    max_price: number;
    products: SimilarProduct[];
};

export type GenericResponse<T> = {
    success: boolean;
    message: string;
    data: T;
    status: number;
};

export type SortType =
    ''
    | 'newest'
    | 'cheapest'
    | 'most_expensive'
    | 'most_sales'
    | 'most_viewed'
    | 'most_popular';

export type ProductFilterOptions = {
    has_guarantee_only?: 'true' | 'false';
    in_stock_only?: 'true' | 'false';
    max_price?: number;
    min_price?: number;
    page?: number;
    limit?: number;
    search?: string;
    sort?: SortType;
    [key: `attribute_${string}`]: string | undefined;
}


