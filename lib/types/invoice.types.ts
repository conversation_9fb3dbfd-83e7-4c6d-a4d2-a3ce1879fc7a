import { CartApiItem } from "../context/cart-context";
import { CartItemAttribute } from "./product.types";

export interface InvoiceProductDetail {
  key: string;
  value: string;
}

export interface InvoiceProduct {
  id: string;
  variant_id: string;
  name: string;
  price: number;
  sale_price: number;
  discount: number;
  quantity: number;
  total: number;
  image: string | null;
  details: InvoiceProductDetail[];
}

export interface InvoiceAddress {
  receiver_name: string;
  receiver_phone: string;
  address: string;
  province: string;
  city: string;
  zip_code: number;
  latitude: number;
  longitude: number;
}

export interface InvoiceData {
  id: string;
  status: string;
  creation_date: string;
  products: InvoiceProduct[];
  total: number;
  total_discount: number;
  address: InvoiceAddress;
}

// Main response type
export interface CreateInvoiceResponse {
  success: boolean;
  message: string;
  status: number;
  data: InvoiceData | {
    cart: string[];
  };
}



// get specefic invoice types 
export interface InvoiceResponse {
  success: boolean;
  message: string;
  status: number;
  data: SpecificInvoiceData;
}
export interface AllInvoicesResponse {
  success: boolean;
  message: string;
  data: {
    invoices: Invoice[];
    pagination: Pagination;
    delivery_status_counts: DeliveryStatusCounts;
  };
  status: number;
}

export interface Invoice {
  id: string;
  invoice_number: string;
  status: 'pending' | 'paid' | string;
  creation_date: string;
  products: Product[];
  products_count: number;
  subtotal: number;
  total_discount: number;
  total: number;
  address: Address;
  delivery_status: 'in_progress' | string;
  transactions: Transaction[];
  pay_method: string;
  discount_code: string | null;
}

export interface Product {
  product_id: string;
  id: number;
  name: string;
  sku: string;
  price: number;
  sale_price: number | null;
  quantity: number;
  discount: number;
  total: number;
  image: string;
  attributes: Attribute[];
  guarantee: string | null;
}

export interface Attribute {
  title: string | null;
  value: string;
}

export interface Address {
  receiver_name: string;
  receiver_phone: string;
  address: string;
  province: string;
  city: string;
  zip_code: string;
  latitude: string;
  longitude: string;
}

export interface Transaction {
  user_id: string;
  status: 'paid' | 'pending' | string;
  amount: number;
  payment_method: 'online' | string;
  payment_gateway: string;
  track_code: string | null;
  description: string;
  paid_at: string | null;
  created_at: string;
}

export interface Pagination {
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
}

export interface SpecificInvoiceData {
  id: string;
  status: string;
  creation_date: string;
  pay_method: string;
  products: CartApiItem[];
  subtotal: number;
  total_discount: number;
  products_count: number;
  total: number;
  delivery_status: string;
  address: InvoiceAddress;
  invoice_number: string
  transactions: Transaction[];
}


export interface ProductAttribute {
  type: string;
  title: string;
  value: string;
  extra_data: {
    hex: string;
  } | null;
}

export interface InvoiceAddress {
  receiver_name: string;
  receiver_phone: string;
  address: string;
  province: string;
  city: string;
  zip_code: number;
  latitude: number;
  longitude: number;
}
export interface DeliveryStatusCounts {
  in_progress: number
  sent: number
  done: number
  canceled: number
}
