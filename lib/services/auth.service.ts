import {jwtDecode} from "jwt-decode";
import fetchApi from "@/lib/fetch-api";
import {ApiService, ResponseResult} from "@/lib/types/action-types";

class AuthService {

    auth<T>(arg: ApiService): Promise<ResponseResult<T>> {
        return fetchApi.post('auth', arg?.payload || {})
    }

    verification<T>(arg: ApiService): Promise<ResponseResult<T>> {
        return fetchApi.post("verification", arg?.payload || {})
    }

    getUser<T>(): Promise<ResponseResult<T>> {
        return fetchApi.get("user")
    }

    chargeWallet(arg: ApiService) {
        return fetchApi.post("user/charge-wallet", arg?.payload || {})
    }

    decodeJwtToken(token: string) {
        // console.log("#####################", token);
        
        const jwtToken = token.startsWith('Bearer ') ? token.split(' ')[1] : token;
        // const jwtToken = token.split(' ')[1];
        // console.log("^^^^^^^^^^^^^^^^^^^^^^^^^^^", jwtToken);
        return jwtDecode(jwtToken);
    }

    jwtTokenIsValid(token: string) {
        const decoded = this.decodeJwtToken(token)
        if (decoded && decoded.exp) {
            const exp = decoded.exp * 1000;
            const now = Date.now();
            return now < exp
        }
        return false
    }
}

export default new AuthService()
