
import {cookies} from "next/headers";
import {AUTHORIZATION} from "@/lib/constants";
import envConfig from "@/lib/config-env";

class CookieService {


    static async setAuthorizationToken(token: string, expire?: number) {
        console.log('cookie is created', token, expire);
        const decodedToken = token.replace(/^Bearer\s/, ''); 
        const env = envConfig()
        let expireAt: Date | undefined;

        if (expire) {
            expireAt = new Date(expire * (expire > 10000000000 ? 1 : 1000));
        } else {
            // 1 hour from now for when we read token from result page param
            expireAt = new Date(Date.now() + 60 * 60 * 1000);
        }
        (await cookies()).set(AUTHORIZATION, decodedToken, {
            httpOnly: true,
            secure: env.NODE_ENV === 'production',
            sameSite: 'lax',
            expires: expireAt,
            path: "/"
        });
    }

    static async getAuthorizationToken() {

        const cookieStore = await cookies();
        const token = cookieStore.get(AUTHORIZATION); // Replace with your cookie name

        return token ? token.value : null;
    }

    static async deleteAuthorizationToken() {
        const cookieStore = await cookies();
        if (cookieStore.has(AUTHORIZATION)) {
            cookieStore.delete(AUTHORIZATION);
        }
    }
}

export default CookieService
