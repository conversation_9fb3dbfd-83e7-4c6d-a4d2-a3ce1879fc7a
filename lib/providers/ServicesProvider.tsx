// context/RefreshContext.tsx
"use client";
import { getServicesAction } from "@/actions/other.action";
import React, {
    createContext,
    useState,
    useEffect,
    useContext,
    ReactNode,
} from "react";

interface GetServicesStatusProps {
    data: any; // Replace with your actual data type

}

const ServicesStatus = createContext<GetServicesStatusProps | undefined>(undefined);

export const ServicesProvider = ({ children }: { children: ReactNode }) => {
    const [data, setData] = useState<any>(null);
    
    const updateData = async () => {
        // setIsLoading(true); // Start loading
        try {
          const res = await getServicesAction() // Change this endpoint as needed
          
          setData(res);
          if (res.success) {
            // throw new Error("Failed to fetch data");
          }
          
        } catch (error) {
          console.error("Error fetching data:", error);
        }
        // setIsLoading(false); // Stop loading
      };
    
      //  Fetch initial data on mount
      useEffect(() => {
        updateData();
      }, []);
    

    return (
        <ServicesStatus.Provider value={{ data }}>
            {children}
        </ServicesStatus.Provider>
    );
};

export const useServiceStatus = () => {
    const context = useContext(ServicesStatus);
    if (!context) {
        throw new Error("useRefresh must be used within a RefreshProvider");
    }
    return context;
};
