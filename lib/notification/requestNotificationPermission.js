export async function requestNotificationPermission() {
  if (!('Notification' in window)) {
    throw new Error('This browser does not support notifications.');
  }

  const current = Notification.permission;

  if (current === 'granted') {
    return 'granted';
  }

  if (current === 'denied') {
    throw new Error('User has denied notification permissions.');
  }

  try {
    const permission = await Notification.requestPermission();
    // if (permission !== 'granted') {
    //   throw new Error('Notification permission not granted.');
    // }
    return permission;
  } catch (err) {
    console.error('Permission request failed:', err);
    throw err;
  }
}