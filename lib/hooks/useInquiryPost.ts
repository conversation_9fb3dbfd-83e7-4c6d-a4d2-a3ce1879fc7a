'use client'

import {StatusCodes} from "http-status-codes";
import toast from "react-hot-toast";
import {useCallback, useState} from "react";
import {useAuth} from "@/lib/hooks/useAuth";
import usePathUrlHelper from "./usePathUrlHelper";
import {ViolationQueryParams} from "@/lib/types/types";
import {postInquireVehicle} from "@/actions/inquiry.action";

type InquiryPostResult = {
    status?: number,
    href?: string,
    message?: string,
    success: boolean
}

export default function useInquiryPost() {

    const {toLoginUrl, toPaymentUrl, toInquiryResultUrl} = usePathUrlHelper();
    const {reFetchUser} = useAuth()
    const [isLoading, setIsLoading] = useState(false)

    const mutate = useCallback(async (params: ViolationQueryParams): Promise<InquiryPostResult> => {
        setIsLoading(true)
        const {
            inquiry,
            middle,
            alphabet,
            isMotor,
            nationalCode,
            phoneNumber,
            right,
            withDetails,
            left,
            reInquiry
        } = params;

        const actionResult = await postInquireVehicle({
            type: isMotor === 'true' ? 'motor' : 'car',
            details: withDetails === 'true' ? {phone: phoneNumber!, national_id: nationalCode!} : undefined,
            plaque: isMotor === 'true' ?
                {
                    left,
                    right,
                }
                : {
                    left,
                    mid: middle,
                    right,
                    alphabet,
                },
            inquiry: reInquiry === 'true'
        })

        if (!actionResult.success) {
            const qparams: ViolationQueryParams = {
                left,
                alphabet: isMotor === 'true' ? undefined : alphabet,
                middle: isMotor === 'true' ? undefined : middle,
                right,
                isMotor: isMotor === 'true' ? 'true' : 'false',
                nationalCode: nationalCode,
                phoneNumber,
                withDetails,
                inquiry,
                reInquiry: actionResult?.data?.showDialogInquiry ? 'true' : 'false'
            }
            if (actionResult.status === StatusCodes.UNAUTHORIZED) {

                return {
                    href: toLoginUrl({
                        ...qparams,
                        message: "جهت مشاهده نتیجه خلافی ابتدا وارد شوید"
                    }, {currentUrlQuery: false}),
                    message: actionResult.message,
                    success: false,
                    status: actionResult.status
                }
            }

            if (actionResult.status === StatusCodes.PAYMENT_REQUIRED) {
                return {
                    href: toPaymentUrl({
                        ...qparams,
                        message: 'اعتبار شما کافی نیست. برای استفاده از خدمات اعتبار خودتان را افزایش دهید.'
                    }, {currentUrlQuery: false}),
                    message: actionResult.message,
                    success: false,
                    status: actionResult.status
                }
            }
            setIsLoading(false)
            return {
                message: actionResult.message,
                success: false,
                status: actionResult.status || 500
            };
        }

        await reFetchUser();
        return {
            success: true,
            href: toInquiryResultUrl({
                traceNumber: actionResult.data!.trace_number!,
                reInquiry: actionResult?.data?.showDialogInquiry ? 'true' : 'false',
                isMotor
            }),

        }

    }, [toInquiryResultUrl, toLoginUrl, toPaymentUrl, reFetchUser])

    return {mutate, isLoading, setIsLoading}
}
