import APIError from "@/lib/types/classes/api-error";
import {ActionResult} from "@/lib/types/action-types";
import {StatusCodes} from "http-status-codes";
import {ReadonlyURLSearchParams, redirect} from "next/navigation";
import {LOGIN_PATH} from "@/lib/routes";
import {logOut} from "@/actions/auth.action";
import {ProductFilterOptions} from "@/lib/types/product.types";

export async function handleActionError(error: ActionResult) {
    if (error.status === StatusCodes.UNAUTHORIZED) {
        await logOut();
        throw redirect(LOGIN_PATH)
    }
}

export function handleActionErrorResponse(error: APIError): ActionResult<any> {
    console.log("errrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrr",error);
    
    return {
        success: false,
        message: error.message,
        status: error.status,
        callbackUrl: error.callbackUrl,
    }
}

export const decodeSearchParams = (searchParams: Record<string, string | string[]>): Record<string, string | string[]> => {
    return Object.fromEntries(
        Object.entries(searchParams).map(([key, value]) => [
            key,
            Array.isArray(value) ? value.map(decodeURIComponent) : decodeURIComponent(value),
        ])
    );
}

export function formatWithComma(value: string) {
    if (!value || isNaN(Number(value))) return value
    return Number(value.replace(/,/g, "")).toLocaleString();
}

export function toQueryParams(options: ProductFilterOptions): string {
    const params = new URLSearchParams();

    Object.entries(options).forEach(([key, value]) => {
        if (value) {
            params.append(key, String(value));
        }
    });

    return params.toString();
}

export function toQueryString(
    params: any,
    mobileUsePagination: boolean
): string {
    const queryParams = new URLSearchParams(); // ✅ کاملاً جدید و خالی

    Object.entries(params).forEach(([key, value]) => {
        if (key === 'limit') return; // حذف از کوئری
        if (key === 'page' && !mobileUsePagination) return;

        if (value !== undefined && value !== null && value !== '') {
            queryParams.set(key, String(value));
        }
    });

    return queryParams.toString();
}

export function getBaseUrl(){
    if (process.env.NODE_ENV === 'development') {
        return 'http://127.0.0.1:8000/api/v1/';
    } else {
        return 'https://shop-khodrox.liara.run/api/v1/';
    }
}