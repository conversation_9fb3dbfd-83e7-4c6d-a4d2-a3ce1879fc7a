<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="72.285" height="72.285" viewBox="0 0 72.285 72.285">
  <defs>
    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffd143"/>
      <stop offset="1" stop-color="#f8be0b"/>
    </linearGradient>
    <filter id="Path_196406" x="0.001" y="0" width="72.285" height="72.285" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-color="#f7bc06"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-3" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0.522"/>
    </linearGradient>
  </defs>
  <g id="Group_90659" data-name="Group 90659" transform="translate(16767.809 600.619)">
    <g id="Group_90657" data-name="Group 90657" transform="translate(-16758.809 -594.619)">
      <g id="Group_90658" data-name="Group 90658" transform="translate(0 0)">
        <g id="Group_89365" data-name="Group 89365" transform="translate(0 0)">
          <g id="Group_89173" data-name="Group 89173" transform="translate(0 0)">
            <g id="icon_txt2" data-name="icon+txt2" transform="translate(0 0)">
              <g transform="matrix(1, 0, 0, 1, -9, -6)" filter="url(#Path_196406)">
                <path id="Path_196406-2" data-name="Path 196406" d="M82.251,54.967A27.143,27.143,0,1,1,55.108,27.824,27.135,27.135,0,0,1,82.251,54.967Z" transform="translate(-18.97 -21.82)" opacity="0.11" fill="url(#linear-gradient)"/>
              </g>
              <g id="Group_89119" data-name="Group 89119" transform="translate(4.67 4.67)">
                <path id="Path_196407" data-name="Path 196407" d="M76.593,53.979A22.473,22.473,0,1,1,54.12,31.506,22.467,22.467,0,0,1,76.593,53.979Z" transform="translate(-31.647 -31.506)" fill="url(#linear-gradient)"/>
              </g>
            </g>
          </g>
        </g>
      </g>
    </g>
    <g id="forbidden-sign-svgrepo-com" transform="translate(-16746.754 -580.114)">
      <path id="Path_196438" data-name="Path 196438" d="M13.433,10.137A7.111,7.111,0,0,0,5.221,4.518,6.482,6.482,0,0,0,.682,8.264,1.973,1.973,0,0,0,.308,9.273,7.634,7.634,0,0,0,.092,12.6c.533,3.3,3.746,5.172,6.9,5.143a.3.3,0,0,0,.158,0,8.173,8.173,0,0,0,.836-.072.221.221,0,0,0,.144-.1,8.386,8.386,0,0,0,4.193-2.406A5.807,5.807,0,0,0,13.433,10.137ZM2.959,13a5.217,5.217,0,0,1-.3-2.161,4.071,4.071,0,0,1,4.437-3.76A3.794,3.794,0,0,1,8.232,7.3c-.2.274-.4.591-.562.778-.706.735-1.47,1.455-2.219,2.176-.663.648-1.325,1.3-1.931,1.988A4.554,4.554,0,0,0,2.959,13Zm3.472,2.507a.178.178,0,0,0-.115.058,3.406,3.406,0,0,1-1.527-.447c.216-.173.4-.432.562-.576.692-.692,1.354-1.412,2.031-2.132.605-.634,1.225-1.225,1.873-1.8A7.545,7.545,0,0,0,10.681,9.23a4.212,4.212,0,0,1,.619,2.089C11.344,13.926,8.606,15.209,6.431,15.511Z" transform="translate(0 -4.383)" fill="url(#linear-gradient-3)"/>
    </g>
    <path id="car-crash-svgrepo-com_1_" data-name="car-crash-svgrepo-com(1)" d="M37.82,20.4,37.2,22.708a3.125,3.125,0,0,1-.875,1.464,3.335,3.335,0,0,1-.437.349l-.7,2.6a1.594,1.594,0,0,1-1.954,1.128L31.7,27.841a1.6,1.6,0,0,1-1.128-1.954l.413-1.541-12.331-3.3-.413,1.541a1.6,1.6,0,0,1-1.954,1.128L14.741,23.3a1.595,1.595,0,0,1-1.128-1.954l.7-2.6a3.322,3.322,0,0,1-.2-.521,3.124,3.124,0,0,1-.026-1.705L14.7,14.2a3.168,3.168,0,0,1,1.84-2.1,3.208,3.208,0,0,1,.922-.235l.9-1.208,1.6-2.143A5.617,5.617,0,0,1,21.86,6.9a5.475,5.475,0,0,1,.75-.324,5.536,5.536,0,0,1,1.608-.3,5.63,5.63,0,0,1,1.667.189L32,8.1a5.62,5.62,0,0,1,1.538.67A5.556,5.556,0,0,1,36.1,12.833l.317,2.657.179,1.5a3.216,3.216,0,0,1,1.134,1.492A3.149,3.149,0,0,1,37.82,20.4ZM18.76,14.463a1.505,1.505,0,0,0-1.953,1.123,1.5,1.5,0,0,0,1.129,1.949c.925.248,2.271.773,2.518-.149A3.069,3.069,0,0,0,18.76,14.463Zm14.491,1.4-.317-2.657a2.393,2.393,0,0,0-1.757-2.028L25.059,9.543a2.394,2.394,0,0,0-2.536.878l-1.6,2.143,8.577,2.3Zm.922,2.725a3.07,3.07,0,0,0-2.93,1.685c-.247.922,1.181,1.14,2.106,1.388a1.591,1.591,0,1,0,.823-3.073Z" transform="translate(-16754.078 -581.977)" fill="url(#linear-gradient-3)"/>
  </g>
</svg>
