"use server"

import { apiClient } from "@/lib/apiClient";
import { GenericResponse, ProductFilterOptions, ProductResponseData } from "@/lib/types/product.types";
import { toQueryParams } from "@/utils/helpers";

export async function getSimilarProductsAction(slug: string) {
    try {
        const response = await apiClient(`products/${slug}/similar`, {
            base: "alt",
            method: "GET",
        })
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error getting similar products:", error);
        return { success: false, error: "Failed to get similar products" };
    }
}


export async function getProducts(productFilter: ProductFilterOptions): Promise<GenericResponse<ProductResponseData>> {
    const {has_guarantee_only, in_stock_only, page = 1, search, sort, min_price, max_price, limit = 9} = productFilter;
    const url = toQueryParams({
        page,
        limit,
        search,
        sort,
        min_price,
        max_price,
        in_stock_only,
        has_guarantee_only,
    });
    // const env = envConfig();
    // const baseUrl = getBaseUrl()
    // console.log(env, '<<<<<<<<<<<<<<<<<<<<<<',getBaseUrl());
    // const res = await fetch(`${baseUrl}products${url ? '?' + url : ''}`, {
    //     method: 'GET',
    //     headers: {
    //         'Content-Type': 'application/json',
    //         "Accept": "application/json"
    //     },
    // });
    const res = await apiClient(`products${url ? '?' + url : ''}`, {
        base: "alt",
        method: "GET",
    })

    if (!res.ok) {
        throw new Error(`Failed to fetch product: ${res.status} ${res.statusText}`);
    }

    const data = await res.json();
    return data;
}


export async function getProductsByCategory(categorySlug: string, productFilter: ProductFilterOptions): Promise<GenericResponse<ProductResponseData>> {
    const url = toQueryParams(productFilter);
    // const env = envConfig();
    // const baseUrl = getBaseUrl()
    // const res = await fetch(`${baseUrl}categories/${categorySlug}/products${url ? '?' + url : ''}`, {
    //     method: 'GET',
    //     headers: {
    //         'Content-Type': 'application/json',
    //     },
    // });
    const res = await apiClient(`categories/${categorySlug}/products${url ? '?' + url : ''}`, {
        base: "alt",
        method: "GET",
    })

    if (!res.ok) {
        throw new Error(`Failed to fetch productsByCategory: ${res.status} ${res.statusText}`);
    }

    const data = await res.json();
    console.log("-----------data------------",data);
    
    return data;
}

export async function getCategories () {
    try {
        const response = await apiClient("categories", {
            base: "alt",
            method: "GET",
        })
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error getting categories:", error);
        return { success: false, error: "Failed to get categories" };
    }
}

export async function getCategoryBreadCrumb (categorySlug: string) {
    try {
        const response = await apiClient(`categories/${categorySlug}/breadcrumb`, {
            base: "alt",
            method: "GET",
        })
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error getting categories:", error);
        return { success: false, error: "Failed to get categories" };
    }
}
export async function getCategoriesBySlug (categorySlug: string) {
    try {
        const response = await apiClient(`categories/${categorySlug}/tree`, {
            base: "alt",
            method: "GET",
        })
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error getting categories:", error);
        return { success: false, error: "Failed to get categories" };
    }
}
export async function getCategoryAttributes (categorySlug: string) {
    try {
        const response = await apiClient(`categories/${categorySlug}/searchables`, {
            base: "alt",
            method: "GET",
        })
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error getting categories:", error);
        return { success: false, error: "Failed to get categories" };
    }
}