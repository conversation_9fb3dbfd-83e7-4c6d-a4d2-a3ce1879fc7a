"use server"
import { apiClient } from "@/lib/apiClient";
import { Variation } from "@/lib/types/product.types";

export async function addToCard(varient:Variation) {
    try {
        // const response = await fetch("https://shop-khodrox.liara.run/api/v1/cart", {
        //     method: "POST",
        //     headers: {
        //         "Authorization": "matin_token",
        //         "X-Application-Token": "matin_token",
        //         "Content-Type": "application/json"
        //     },
        //     body: JSON.stringify({
        //         "variant_id": varient.id,
        //         "quantity": 1
        //     })
        // });
        const response = await apiClient("cart", {
            headers: {
                "Authorization": "matin_token",
                // "X-Application-Token": "matin_token",
                "Content-Type": "application/json"
            },
            base: "alt",
            method: "POST",
            body: {
                variant_id: varient.id,
                quantity: 1
            },            
        })
        console.log(response);
        const data = await response.json();
        return { success: response.ok, data };
    } catch (error) {
        console.error("Error adding to cart:", error);
        return { success: false, error: "Failed to add to cart" };
    }
}
export async function decreaseFromCartAction(varient:string) {
    try {
        // const response = await fetch(`https://shop-khodrox.liara.run/api/v1/cart/${varient}`, {
        //     method: "DELETE",
        //     headers: {
        //         "Authorization": "matin_token",
        //         "X-Application-Token": "matin_token",
        //         "Content-Type": "application/json"
        //     }            
        // });
        const response = await apiClient(`cart/${varient}`, {           
            base: "alt",
            method: "DELETE",
        })
        console.log(response);
        
        const data = await response.json();
        return { success: response.ok, data };
    } catch (error) {
        console.error("Error adding to cart:", error);
        return { success: false, error: "Failed to add to cart" };
    }
}
export async function getUserCart() {
    try {
        // const response = await fetch("https://shop-khodrox.liara.run/api/v1/cart", {
        //     method: "GET",
        //     headers: {
        //         "Authorization": "matin_token",
        //         "X-Application-Token": "matin_token",
        //         "Content-Type": "application/json"
        //     }
        // });
        const response = await apiClient("cart", {
            base: "alt",
            method: "GET",
        })
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error adding to cart:", error);
        return { success: false, error: "Failed to add to cart" };
    }
}
interface CartItems {
    variant_id: string
    quantity: number
}
export async function addMultipleToCart(cartItems: CartItems[]) {
    console.log(cartItems);
    try {
        // const response = await fetch("https://shop-khodrox.liara.run/api/v1/bulk-cart", {
        //     method: "POST",
        //     headers: {
        //         "Authorization": "matin_token",
        //         "X-Application-Token": "matin_token",
        //         "Content-Type": "application/json"
        //     },
        //     body: JSON.stringify({
        //         items: cartItems
        //     })
        // });
        const response = await apiClient("bulk-cart", {
            base: "alt",
            method: "POST",
            body: {
                items: cartItems
            },
        })
        
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error adding to cart:", error);
        return { success: false, error: "Failed to add to cart" };
    }
}