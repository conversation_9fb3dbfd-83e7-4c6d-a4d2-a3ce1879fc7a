"use server"

import { apiClient } from "@/lib/apiClient";


export async function getFavorites( page?: number , limit?: number, sort?: string ) {
    try {
        const response = await apiClient(`products/favorite?${page ? `?page=${page}` : ''}&${limit ? `&limit=${limit}` : ''}&${sort ? `&sort=${sort}` : ''}`, {
            base: "alt",
            method: "GET",
        })
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error getting favorites:", error);
        return { success: false, error: "Failed to get favorites" };
    }
}

export async function deleteFromFavorites(productSlug: string) {
    try {
        
        const response = await apiClient(`products/favorite/${productSlug}`, {
            base: "alt",
            method: "DELETE",
        })
        console.log(response);
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error deleting from favorites:", error);
        return { success: false, error: "Failed to delete from favorites" };
    }
}
export async function addToFavorites(productId: number) {
    try {
        
        const response = await apiClient(`products/favorite`, {
            base: "alt",
            method: "POST",
            body: {
                product_id: productId
            },
        })
        // console.log(response);
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error adding to favorites:", error);
        return { success: false, error: "Failed to add to favorites" };
    }
}