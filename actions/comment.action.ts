"use server"

import { apiClient } from "@/lib/apiClient";
import { createProductCommentPayload } from "@/lib/types/comment.types";

export async function createProductComment(commentData: createProductCommentPayload) {
    try {
        // const response = await fetch("https://shop-khodrox.liara.run/api/v1/products/comments", {
        //     method: "POST",
        //     headers: {
        //         "Authorization": "matin_token",
        //         "Content-Type": "application/json",
        //         "X-Application-Token": "matin_token",
        //         "Accept": "application/json"
        //     },
        //     body: JSON.stringify(commentData)
        // });
        const response = await apiClient("products/comments", {
            base: "alt",
            method: "POST",
            body: commentData,
        })
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error creating comment:", error);
        return { success: false, error: "Failed to create comment" };
    }

}
export async function createArticleComment(commentData: createProductCommentPayload) {
    try {
        // const response = await fetch("https://shop-khodrox.liara.run/api/v1/article/comments", {
        //     method: "POST",
        //     headers: {
        //         "Authorization": "matin_token",
        //         "Content-Type": "application/json",
        //         "X-Application-Token": "matin_token",
        //         "Accept": "application/json"
        //     },
        //     body: JSON.stringify(commentData)
        // });
        const response = await apiClient("article/comments", {
            base: "alt",
            method: "POST",
            body: commentData,
        })
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error creating comment:", error);
        return { success: false, error: "Failed to create comment" };
    }

}

export async function getArticleComments(articleId: string) {
    try {
        const response = await apiClient(`article/${articleId}/comments`, {
            base: "alt",
            method: "GET",
        })
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error getting comments:", error);
        return { success: false, error: "Failed to get comments" };
    }
}

