"use client"
import Link from "next/link";
import DisplayServiceIcon from "@/components/Services/DisplayServiceIcon";
import {
    CAR_ID_DOCUMENTS_PATH,
    CAR_INSURANCE_PATH,
    CAR_TICKETS_PATH,
    CAR_VIOLATION_IMAGE_PATH,
    DRIVING_LICENSE_POINT_PATH,
    DRIVING_LICENSE_STATUS_PATH,
    MOTOR_TICKETS_PATH,
    PLATE_HISTORY_PATH,
} from "@/lib/routes";
import {ServiceColorVariantType, ServiceStatusType} from "@/lib/types/types";
// import envConfig from "@/lib/config-env";



import { useServiceStatus } from "@/lib/providers/ServicesProvider";
import ServiceIcon from "../common/svg/services/ServiceIcon";

interface ServiceItemProps {
    href: string;
    name: string;
    icon: React.ReactNode;
    colorVariant?: ServiceColorVariantType;
    status: ServiceStatusType;
}


const ServiceItem: React.FC<ServiceItemProps> = ({href, name, icon, colorVariant, status}) => {

    const isDisabled = status === 'DEACTIVE'
    return (
        <li className="w-full">
            {isDisabled ? (
                <span className="flex flex-col items-center gap-y-3">
                    <DisplayServiceIcon variant={colorVariant} status={status}>{icon}</DisplayServiceIcon>
                    <Link href={href} className="text-xs text-center px-1">{name}</Link>
                </span>
            ) : (
                <Link href={href} className="flex flex-col items-center gap-y-3">
                    <DisplayServiceIcon variant={colorVariant} status={status}>{icon}</DisplayServiceIcon>
                    <span className="text-xs text-center px-1">{name}</span>
                </Link>
            )}
        </li>
    );
};


export default function ServiceList() {
   
    const { data } = useServiceStatus()
    
    const services_status = data?.data?.services || {}
   
    
    
    const services: ReadonlyArray<ServiceItemProps> = [
        {
            icon: <ServiceIcon imagePath="car-crash"/>,
            href: CAR_TICKETS_PATH, colorVariant: "yellow",
            name: "خلافی خودرو",
            status: services_status?.car_tickets || "ACTIVE"
        },
        {
            icon: <ServiceIcon imagePath="motorcycle"/>,
            href: MOTOR_TICKETS_PATH,
            colorVariant: "blue",
            name: "خلافی موتور سیکلت",
            status: services_status?.motor_tickets || "ACTIVE"
        },
        {
            icon: <ServiceIcon imagePath="service-camera"/>,
            href: CAR_VIOLATION_IMAGE_PATH,
            colorVariant: "purple",
            name: "تصویر تخلفات رانندگی",
            status: services_status?.car_violation_image || "DEACTIVE"
        },
        {
            icon: <ServiceIcon imagePath="car-overrun"/>,
            href: CAR_INSURANCE_PATH,
            colorVariant: "green",
            name: "استعلام بیمه شخص ثالث",
            status: services_status?.car_insurance || "DEACTIVE"
        },
        {
            icon: <ServiceIcon imagePath="certificate-service"/>,
            href: DRIVING_LICENSE_STATUS_PATH,
            colorVariant: "emerald",
            name: "وضعیت گواهینامه",
            status: services_status?.driving_license_status || "DEACTIVE"
        },
        {
            icon: <ServiceIcon imagePath="document-service"/>,
            href: CAR_ID_DOCUMENTS_PATH,
            colorVariant: "blue",
            name: "وضعیت کارت و سند خودرو",
            status: services_status?.carid_decuments || "DEACTIVE"
        },
        {
            icon: <ServiceIcon imagePath="Inquiry-minus-service"/>,
            href: DRIVING_LICENSE_POINT_PATH,
            colorVariant: "red",
            name: "استعلام نمره منفی",
            status: services_status?.driving_license_point || "DEACTIVE"
        },
        {
            icon: <ServiceIcon imagePath="history-service" />,
            href: PLATE_HISTORY_PATH,
            colorVariant: "indigo",
            name: "استعلام تاریخچه پلاک",
            status: services_status?.plate_history || "DEACTIVE"
        },
        {
            icon: <ServiceIcon imagePath="highway-center"/>,
            href: "#",
            colorVariant: "indigo",
            name: "استعلام عوارض آزاد راه",
            status: services_status?.freeway || "DEACTIVE"
        },
        {
            icon: <ServiceIcon imagePath="accident-service"/>,
            href: "#",
            colorVariant: "lime",
            name: "خرید بیمه بدنه",
            status: services_status?.body_insurance || "DEACTIVE"
        },
    ];

    return (
        <ul className="w-full grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-y-10 ">
            {services.map((service, index) => (
                <ServiceItem
                    key={index}
                    {...service}
                />
            ))}
        </ul>
    );
}
