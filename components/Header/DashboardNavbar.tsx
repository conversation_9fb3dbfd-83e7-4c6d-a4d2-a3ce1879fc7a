"use client";

import { useEffect, useState } from "react";
import EditIcon from "../common/svg/EditIcon";
import SearchIcon from "../common/svg/SearchIcon";
import AlertIcon from "../common/svg/AlertIcon";
import { HeartIcon, Menu, UserIcon, X } from "lucide-react";
import Sidebar from "./Sidebar";
import { useAuth } from "@/lib/hooks/useAuth";
import Link from "next/link";
import { getUserProfile } from "@/actions/userProfile.action";
import Image from "next/image";
import NotificationsList from "../Dashboard/notifications/NotificationsList";

const DashboardNavbar = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [profilePicture, setProfilePicture] = useState(null)
  const { userData } = useAuth();
  useEffect(() => {
    async function fetchUser() {
      const user = await getUserProfile()
      setProfilePicture(user?.data?.profile_image)


    }
    fetchUser()
  }, [])

  return (
    <>
      <nav
        className="bg-white shadow-md md:rounded-xl lg:py-2 px-5 lg:h-24 py-4 flex items-center justify-between">
        {/* User Profile */}
        <Link href={'/dashboard/profile'} className="user-profile flex items-center gap-4">
          <div className="p-1 border-2 relative border-dashed border-gray-200 rounded-full">
            {profilePicture ? (
              <Image src={profilePicture} alt="profile" width={40} height={40} className="rounded-full" />

            ) : (
              <UserIcon className="lg:w-10 lg:h-10 w-8 h-8" />
            )}
            {/* <UserIcon className="lg:w-10 lg:h-10 w-8 h-8"/> */}
          </div>
          <div className="flex flex-col gap-1">
            <div className="flex gap-4">
              <span
                className="lg:text-base text-sm font-bold"> {userData?.fullName || " بدون نام کاربری "} </span>
              <span>
                <EditIcon />
              </span>
            </div>
            <span className="text-sm"> {userData?.phone} </span>
          </div>
        </Link>

        {/* Search & Icons */}
        <div className="flex gap-4 items-center lg:w-[28rem]">
          {/* Search Input (Hidden on Mobile) */}
          <div className="block relative w-full max-md:hidden">
            <input
              type="text"
              placeholder="جستجو..."
              className="w-full py-3 pl-4 pr-10 text-gray-500 bg-gray-100 rounded-full outline-none
              focus:ring-2 focus:ring-gray-300 placeholder-gray-400 text-sm text-right"
              disabled
            />
            <SearchIcon className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 text-lg" />
          </div>

          
          <div className="flex gap-2">
            {/* Menu Button on Mobile, HeartIcon on Desktop */}
            <button
              onClick={() => setIsSidebarOpen(true)}
              className="bg-gray-100 hover:bg-gray-200 transition-all rounded-full flex items-center justify-center w-10 h-10 lg:hidden"
            >
              <Menu className="size-6" />
            </button>

            {/* Heart Icon (Only on Desktop) */}
            <Link href={"/dashboard/favorites"} className="hidden lg:flex bg-gray-100 hover:bg-gray-200 transition-all rounded-full items-center justify-center w-10 h-10">
              <HeartIcon className="md:size-6 size-4" />
            </Link>

            {/* Alert Icon */}
           
            <button
              onClick={() => setDrawerOpen(true)}
              className="bg-gray-100 hover:bg-gray-200 transition-all rounded-full flex items-center justify-center w-10 h-10"
            >
              <AlertIcon className="lg:size-6 size-4" />
            </button>

            {drawerOpen && (
              <div
                className="fixed inset-0 bg-black/30 z-40"
                onClick={() => setDrawerOpen(false)}
              />
            )}

            <div
              className={`fixed top-0 left-0 h-full w-80 max-w-full bg-white shadow-lg z-50 transform transition-transform duration-300 overflow-y-auto ${drawerOpen ? "translate-x-0" : "-translate-x-full"
                }`}
            >
              <div className="p-4 flex items-center justify-between border-b">
                <h2 className="text-lg font-semibold m-0">پیام های من</h2>
                <button
                  onClick={() => setDrawerOpen(false)}
                  className="text-sm text-gray-500 hover:text-black"
                >
                  <X size={18} />
                </button>
              </div>
              <NotificationsList />
            </div>
          </div>
        </div>
      </nav>

      {/* Sidebar Overlay & Animated Sidebar */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-50"
          onClick={() => setIsSidebarOpen(false)}
        ></div>
      )}
      <div
        className={`fixed top-0 right-0 h-full bg-white w-80 shadow-lg z-50 transform ${isSidebarOpen ? "translate-x-0" : "translate-x-full"
          } transition-transform duration-300 ease-in-out`}
      >
        {/* Close Button */}
        {/* <button onClick={() => setIsSidebarOpen(false)} className="absolute top-4 left-4">
                    <X className="size-6" />
                </button> */}

        {/* Sidebar Component */}
        <Sidebar onClose={setIsSidebarOpen} />
      </div>
    </>
  );
};

export default DashboardNavbar;
