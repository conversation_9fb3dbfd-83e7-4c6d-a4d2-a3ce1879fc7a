"use client";
import { useState } from "react";
import NavbarItems from "./NavbarItems"


const Sidebar = ({ onClose }: { onClose?: (isOpen: boolean) => void }) => {
    //   const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    
    return (
        <div className='md:w-[17rem] w-[20rem] h-auto '>
            <div className='max-md:mt-12 md:bg-yellow-100 md:bg-gray-200 md:border h-auto md:border-gray-200  rounded-xl relative'>
               <NavbarItems onClose={() => onClose?.(false)} />
            </div>

        </div>
    )
}

export default Sidebar