import { ArrowRight, BellRing, ChartSpline, EllipsisVertical, LayoutList, ScanLine, SearchIcon, Share2, X } from 'lucide-react'
import React, { useState } from 'react'
import CartIcon from '../common/svg/CartIcon'
import { CartApiItem } from '@/lib/context/cart-context'

import ProductFilterMobileDrawer from '../shop/ProductFilterMobileDrawer'
import SearchMobileWrapper from '../common/SearchMobileWrapper'
import CustomButton from '../UI/CustomButton'
import MobileBottomSheet from '../UI/MobileBottomSheet'
import Logo from "@/public/assets/images/logo.png"
import Image from 'next/image'
import { usePathname, useSearchParams } from 'next/navigation'
import toast from 'react-hot-toast'
import Link from 'next/link'

type ProductHeaderProps = {
    cartItems: CartApiItem[]
}

const ProductHeader = ({ cartItems }: ProductHeaderProps) => {
    // const router = useRouter()
    // const [isSearchOpen, setIsSearchOpen] = useState(false)
    const [openSearchDrawer, setOpenSearchDrawer] = useState(false)
    const [isOpen, setIsOpen] = useState(false)
    const pathname = usePathname();
    const params = useSearchParams()
    const product_id = params.get('product_id')
    console.log("pathname: ",pathname);
    console.log(product_id);

    const handleShare = async () => {
        const url = typeof window !== "undefined" ? window.location.origin + `p/${product_id}` : "";

        if (navigator.share) {
            try {
                await navigator.share({
                    title: document.title,
                    url,
                });
                toast.success("لینک با موفقیت به اشتراک گذاشته شد");
            } catch {
                toast.error("خطا");
            }
        } else {
            try {
                await navigator.clipboard.writeText(url);
                toast.success("لینک کپی شد!");
            } catch {
                toast.error("خطا در کپی کردن لینک");
            }
        }
    };
    // const {cartItems} = useCart()

    return (
        <>
            <div className="fixed top-0 left-0 right-0 z-50 bg-white shadow-md rounded-b-sm">
                <div className='flex justify-between items-center h-20 container px-5 mx-auto '>
                    <div className='flex gap-3 items-center'>
                        <Link href={'/category'}>
                            <ArrowRight  />
                        </Link>
                        <Image src={Logo} alt='logo' className='w-32' />
                    </div>
                    <div>
                        <ul className='flex gap-4'>
                            <li>
                                <button
                                    onClick={() => setOpenSearchDrawer(true)}
                                    className="border-2 p-2 hover:bg-gray-200 transition-all rounded-full"
                                >
                                    <SearchIcon className="size-5" />
                                </button>
                            </li>
                            <li>
                                <CustomButton
                                    href='/checkout/cart'
                                    className="bg-transparent relative md:flex border-2 p-1.5 hover:bg-gray-200 transition-all rounded-full items-center justify-center w-[2.5rem] h-[2.5rem]"
                                >
                                    <CartIcon className="size-5" />
                                    <span className="absolute -top-1 right-0">
                                        {cartItems.length ? (
                                            <span className="relative flex size-3">
                                                <span className="absolute inline-flex h-full w-full animate-ping rounded-full bg-red-400 opacity-75"></span>
                                                <span className="relative inline-flex size-3 rounded-full bg-red-500"></span>
                                            </span>
                                        ) : ""}
                                    </span>
                                </CustomButton>
                            </li>
                            <li>
                                <button onClick={() => setIsOpen(true)} className='h-full'>
                                    <EllipsisVertical size={28} />
                                </button>
                            </li>
                        </ul>
                    </div>
                </div>

                {/* Mobile Search Drawer */}
                <div>
                    <ProductFilterMobileDrawer
                        isOpen={openSearchDrawer}
                        onClose={() => setOpenSearchDrawer(false)}
                        showCloseBtn={false}
                    >
                        <SearchMobileWrapper
                            open={openSearchDrawer}
                            onClose={() => setOpenSearchDrawer(false)}
                        />
                    </ProductFilterMobileDrawer>
                </div>
            </div>

            {/* Main content wrapper with padding to avoid overlap */}
            <div className="pt-24">
                {/* Your main page content goes here */}
            </div>

            <MobileBottomSheet className="z-[100]" isOpen={isOpen} onClose={() => setIsOpen(false)}>
                <div className="max-md:w-full p-0 gap-0 rounded-2xl">
                    <div className="relative px-5 flex flex-col ">


                        <div className='mb-5 pb-5'>
                            {/* Single Close Button at top-left (RTL) */}
                            <button
                                onClick={() => setIsOpen(false)}
                                className="absolute left-4 top-4 rounded-full p-1.5 border-2 border-gray-300 hover:bg-gray-100 transition-colors">
                                <X className="h-5 w-5 text-gray-500" />
                            </button>


                        </div>

                        <div className=" border-t-1 mt-3  pb-1">
                            <ul className="flex flex-col gap-1">
                                <li className='border-b py-5 w-full h-full flex items-center gap-3'>
                                    <ChartSpline />   نمودار قیمت
                                </li>
                                <li onClick={handleShare} className='border-b py-5 w-full h-full flex items-center gap-3'>
                                    <Share2 />   اشتراک گذاری
                                </li>
                                <li className='border-b py-5 w-full h-full flex items-center gap-3'>
                                    <ScanLine />  مقایسه کالا
                                </li>
                                <li className='border-b py-5 w-full h-full flex items-center gap-3'>
                                    <LayoutList />  افزودن به لیست
                                </li>
                                <li className='py-3 h-full flex items-center gap-3'>
                                    <BellRing />  اطلاع رسانی شگفت انگیز
                                </li>
                            </ul>
                        </div>
                    </div>

                    {/* <div className="flex border-t border-gray-100 overflow-hidden px-6 mx-auto mb-3"> */}

                        {/* <Link
                            href="/checkout/cart"
                            className="flex-1 rounded-xl rounded-b-md p-4 overflow-hidden text-center bg-primary text-white hover:bg-primary/90 transition-colors font-medium"
                        >
                            مشاهده سبد خرید
                        </Link> */}
                    {/* </div> */}
                </div>
            </MobileBottomSheet>
        </>
    )
}

export default ProductHeader