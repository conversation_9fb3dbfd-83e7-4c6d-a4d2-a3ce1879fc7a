import Image from 'next/image'
import BanerImage from "@/public/assets/images/baner.png"
import BannerImageMobile from "@/public/assets/images/banner-mobile.png"
import HeroSlider from "@/components/Services/HeroSlider";
import HomeSection from "@/components/Header/HomeSection";


const HeroSection = () => {
    return (
        <HomeSection>
            <div className='flex flex-col md:flex-row gap-3 items-center'>
                <div className='w-full min-h-[150px] h-[25vh] md:h-[300px]'>
                    <HeroSlider/>
                </div>
                {/*<div className=' w-full md:w-1/4 h-fit md:h-[300px] rounded-md overflow-hidden'>*/}
                {/*    <Image src={BanerImage} alt='baner' className='hidden md:block h-full'/>*/}
                {/*    <Image src={BannerImageMobile} alt='baner' className='w-full h-full block md:hidden'/>*/}
                {/*</div>*/}
            </div>
        </HomeSection>
    )
}

export default HeroSection