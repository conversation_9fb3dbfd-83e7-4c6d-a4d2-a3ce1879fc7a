"use client";
import Link from 'next/link';
import React, { useState, useEffect, useRef } from 'react';
import Logo from '@/public/assets/images/logo.png';
import Image from 'next/image';
import CustomButton from '../UI/CustomButton';
import UserIcon from '../common/svg/UserIcon';
import MenuIcon from '../common/svg/MenuIcon';
import ProfileIcon from '../common/svg/ProfileIcon';
import { LOGIN_PATH } from "@/lib/routes";
import { useAuth } from "@/lib/hooks/useAuth";
import UserLoggedInButton from "@/components/Header/UserLoggedInButton";
import NavbarItems from './NavbarItems';
// import {menuItems} from "@/components/Header/MenuItems";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { getMenuItems } from '@/utils/menuItems';
import { useServiceStatus } from '@/lib/providers/ServicesProvider';

const Navbar = () => {
    const [isMenuOpen, setIsMenuOpen] = useState(false); // State for mobile menu
    const menuRef = useRef<HTMLDivElement>(null); // Ref for the menu
    const { userData, logoutUser } = useAuth()
    const pathname = usePathname();

    // Close the menu when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
                setIsMenuOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);
    const { data } = useServiceStatus()

    const services_status = data?.data?.services || {}

    const menuItems = getMenuItems(services_status)

    return (
        <header className='shadow-md z-10 relative bg-white '>
            <nav
                className='w-full flex justify-between items-center gap-x-4 h-20 px-4 container max-w-[1280px] mx-auto'>
                {/* Logo and Burger Menu (for mobile) */}
                <div className='h-full flex items-center gap-4'>
                    {/* Burger Menu Icon (visible on mobile) */}
                    <button
                        className='md:hidden'
                        onClick={() => setIsMenuOpen(!isMenuOpen)}
                    >
                        <MenuIcon />
                    </button>

                    <div className='block md:hidden h-[20px] w-[2px] bg-gray-200'></div>

                    {/* Logo */}
                    <div className="h-full w-36 md:w-40 lg:w-44">
                        <Link className="h-full w-full flex justify-center items-center" href="/">
                            <Image
                                src={Logo}
                                alt="logo"
                                width={128}
                                height={64} // 2:1 ratio
                                className="h-auto" // maintain aspect ratio
                                priority
                            />
                        </Link>
                    </div>
                </div>

                {/* Navigation Links  */}
                <div
                    ref={menuRef}
                    className={`fixed md:static md:py-4 top-0 right-0 h-full w-64 bg-white shadow-lg transform transition-transform duration-200 ease-in z-[9999] ${isMenuOpen ? 'translate-x-0' : 'translate-x-full'
                        } md:translate-x-0 md:shadow-none md:w-auto md:bg-transparent`}
                >
                    <ul className='md:h-full max-md:hidden flex flex-wrap md:items-center justify-center md:flex-row md:gap-x-4 lg:gap-x-10 p-3 md:p-0'>
                        {
                            menuItems
                                .filter(item => item.status === 'ACTIVE' && (item.type === 'desktop' || item.type === 'both'))
                                .map((item, index) => (
                                    <li
                                        key={index}
                                        className='group'
                                    >
                                        <Link
                                            className={cn('block w-full h-full whitespace-nowrap text-[#363A3E] transition-all duration-75 text-xs font-light md:text-sm lg:text-base')}
                                            href={item.href}>
                                            {item.title}
                                            <div
                                                className={cn('h-[3px] w-full mt-1 transition-all duration-75 bg-transparent group-hover:bg-yellow rounded-3xl', {
                                                    'bg-yellow': pathname === item.href
                                                })}></div>
                                        </Link>
                                    </li>

                                ))
                        }
                    </ul>
                    <div className='md:hidden mt-10'>
                        <NavbarItems onClose={() => setIsMenuOpen(false)} />

                    </div>
                </div>

                {/* Backdrop for mobile menu (visible only on mobile) */}
                {isMenuOpen && (
                    <div
                        className='fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden'
                        onClick={() => setIsMenuOpen(false)}
                    />
                )}

                {/* User and Cart Icons */}
                <div className='ml-2 flex gap-4'>
                    {/* <button className="relative">
                        <CartIcon className="size-7"/>
                        <span className="absolute -top-0 -right-3">
                <span
                    className="bg-red-500 flex items-center justify-center p-0.5 w-6 h-6 text-sm rounded-full text-white">
                    1
                </span>
            </span>
                    </button> */}
                    {userData === null && (
                        <>
                            <span className='md:!block !hidden'>
                                <CustomButton href={LOGIN_PATH} bgColor='bg-primary'
                                    className='min-w-[125px] whitespace-nowrap gap-2 rounded-3xl'>
                                    <div className="flex items-center gap-1">
                                        <UserIcon className='' />
                                        <span>ورود به حساب</span>
                                    </div>
                                </CustomButton>
                            </span>
                            <Link href={LOGIN_PATH} className='mr-2 md:hidden'><ProfileIcon /></Link>
                        </>
                    )
                    }
                    {
                        userData && (
                            <UserLoggedInButton user={userData!} logOutUser={logoutUser} />
                        )
                    }

                </div>
            </nav>
        </header>
    );
};

export default Navbar;