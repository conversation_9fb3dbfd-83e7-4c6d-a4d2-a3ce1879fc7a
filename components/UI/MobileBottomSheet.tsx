'use client';

import React, { useLayoutEffect, useEffect, useState } from 'react';
import { X } from 'lucide-react';

interface MobileBottomSheetProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  className?: string;
}

const MobileBottomSheet = ({ isOpen, onClose, children, className }: MobileBottomSheetProps) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  // 1) mount/unmount
  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);
    } else {
      // play hide animation first
      setIsAnimating(false);
      const t = setTimeout(() => setIsVisible(false), 300);
      return () => clearTimeout(t);
    }
  }, [isOpen]);

  // 2) trigger the “slide up” _after_ it’s in the DOM
  useLayoutEffect(() => {
    if (isVisible && isOpen) {
      // next tick → play animation
      requestAnimationFrame(() => {
        setIsAnimating(true);
      });
    }
  }, [isVisible, isOpen]);

  if (!isVisible) return null;

  return (
    <div
      className={`fixed inset-0 z-50 md:hidden transition-opacity duration-300 ${className} ${
        isAnimating ? 'opacity-100' : 'opacity-0'
      }`}
    >
      {/* Overlay */}
      <div className="absolute inset-0 bg-black/60" onClick={onClose} />

      {/* Sheet */}
      <div
        className={`
          absolute bottom-0 left-0 right-0 bg-white rounded-t-2xl
          transform transition-transform duration-300 ease-out will-change-transform
          ${isAnimating ? 'translate-y-0' : 'translate-y-full'}
        `}
      >
        {children}
      </div>
    </div>
  );
};

export default MobileBottomSheet;
