import React, {<PERSON>actN<PERSON>, MouseEvent} from 'react';
import Link from 'next/link';
import {<PERSON>Loader} from "react-spinners";
import {cn} from "@/lib/utils";

interface ButtonProps {
    children: ReactNode;
    onClick?: (event: MouseEvent<HTMLButtonElement>) => void;
    className?: string;
    bgColor?: string;
    textColor?: string;
    padding?: string;
    disabled?: boolean;
    type?: 'button' | 'submit' | 'reset';
    width?: string;
    loading?: boolean;
    href?: string;
    target?: '_blank' | '_self' | '_parent' | '_top';
    rel?: string;
    variant?: 'primary' | 'green' | 'gray';

    [key: string]: any;
}

const CustomButton: React.FC<ButtonProps> = ({
                                                 children,
                                                 onClick,
                                                 loading,
                                                 className = '',
                                                 bgColor,
                                                 textColor,
                                                 padding = 'py-2.5',
                                                 disabled = false,
                                                 type = 'button',
                                                 width = "w-full",
                                                 href,
                                                 target,
                                                 rel,
                                                 variant = 'primary',
                                                 ...props
                                             }) => {
    const variantStyles = {
        primary: {bg: 'bg-primary', text: 'text-white'},
        green: {bg: 'bg-green-500', text: 'text-white'},
        gray: {bg: 'bg-gray-400', text: 'text-white'},
    };

    const selectedVariant = variantStyles[variant] || variantStyles.primary;
    const bColor = disabled ? 'bg-gray-300' : bgColor || selectedVariant.bg;
    const tColor = disabled ? 'text-gray-500' : textColor || selectedVariant.text;
    const disabledClass = disabled ? 'cursor-not-allowed' : '';
    const combinedClassName = `w-full ${bColor} ${tColor} ${disabledClass} ${width} ${padding} rounded-2xl ${className} flex items-center gap-2 px-3 justify-center text-xs md:text-sm`.trim();

    if (href) {
        return (
            <Link href={href}
                  target={target}
                  rel={target === '_blank' ? 'noopener noreferrer' : rel}
                  className={cn('block', combinedClassName, className)}
            >
                {loading ? (<BeatLoader color='white' size={10}/>) : children}
            </Link>
        );
    }

    return (
        <button
            type={type}
            className={cn(combinedClassName, className)}
            onClick={onClick}
            disabled={disabled}
            {...props}
        >
            {loading ? (<BeatLoader color={disabled ? 'gray' : 'white'} size={10}/>) : children}
        </button>
    );
};

export default CustomButton;
