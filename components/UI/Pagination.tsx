'use client';
import {ChevronLeft, ChevronRight} from 'lucide-react';
import {cn} from "@/lib/utils";

type PaginationProps = {
    currentPage: number;
    lastPage: number;
    setCurrentPage?: (page: number) => void;
}

const Pagination = ({currentPage = 1, lastPage, setCurrentPage}: PaginationProps) => {

    console.log('pagination')
    console.log({currentPage, lastPage, setCurrentPage});
    const goToPage = (page: number) => {
        setCurrentPage?.(page);
    };

    return (
        <div className="flex items-center justify-center gap-2">
            {
                currentPage > 1 && <button
                    onClick={() => currentPage > 1 && goToPage(currentPage - 1)}
                    className={cn('w-9 h-9 rounded-full bg-white ' +
                        'flex items-center justify-center shadow hover:bg-blue-600 hover:text-white transition', {
                        "bg-blue-500 text-white": currentPage > 3
                    })}
                >
                    <ChevronRight size={16}/>
                </button>}

            {/* Ellipsis */}
            {
                currentPage > 3 && (
                    <span className="text-gray-500 text-sm px-1">...</span>
                )
            }

            {
                [...Array(lastPage)].map((_, i) => {
                    const page = i + 1;

                    const show = (currentPage <= 3 && page <= 3) ||
                        (currentPage >= lastPage - 2 && page >= lastPage - 2) ||
                        (currentPage - 1 === page) ||
                        (currentPage === page) ||
                        (currentPage + 1 === page)

                    if (show) {
                        return (
                            <button
                                key={page}
                                onClick={() => goToPage(page)}
                                className={`w-9 h-9 rounded-full border text-sm flex items-center justify-center transition ${
                                    currentPage === page
                                        ? 'border-blue-500 text-blue-500 '
                                        : 'border-gray-200 text-gray-700 hover:border-blue-300 hover:text-blue-400'
                                }`}
                            >
                                {page}
                            </button>
                        );
                    } else return null;
                })}

            {
                currentPage < lastPage - 2 && (
                    <span className="text-gray-500 text-sm px-1">...</span>
                )}
            {/* Next */}
            {
                currentPage !== lastPage && <button
                    onClick={() => currentPage < lastPage && goToPage(currentPage + 1)}
                    className={cn('w-9 h-9 rounded-full border border-gray-300 text-gray-600\n' +
                        '                flex items-center justify-center hover:bg-gray-100 transition', {
                        "bg-blue-500 text-white hover:bg-gray-100 hover:text-gray-800": lastPage - 2 > currentPage
                    })}
                >
                    <ChevronLeft size={16}/>
                </button>}
        </div>
    );
};

export default Pagination;
