// components/JalaliDatePicker.tsx
'use client'

import DatePicker, { Value } from 'react-multi-date-picker'
import persian from 'react-date-object/calendars/persian'
import persian_fa from 'react-date-object/locales/persian_fa'

interface JalaliDatePickerProps {
  value: Value; 
  setValue: (value: Value) => void; 
}

export default function JalaliDatePicker({ value, setValue }: JalaliDatePickerProps) {
  return (
    <DatePicker
      value={value}
      onChange={setValue}
      calendar={persian}
      locale={persian_fa}
      inputClass='w-full p-3 border border-gray-300 rounded-xl focus:border-primary outline-none'
      calendarPosition='bottom-right'
      format='YYYY/MM/DD'
      placeholder='تاریخ تولد'
    />
  )
}
