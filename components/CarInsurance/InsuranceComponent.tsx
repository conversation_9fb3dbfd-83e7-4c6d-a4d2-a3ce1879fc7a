import Container from "@/components/common/Container";
import {PageDescription} from "@/components/common/PageDescription";
import InsuranceFormContainer from "@/components/CarInsurance/InsuranceFormContainer";
import {ServiceStatusType} from "@/lib/types/types";

type Props = {
    title: string;
    isMotor: boolean;
    status: ServiceStatusType
}

export default function InsuranceComponent({title, isMotor, status}: Props) {
    return (
        <>
            <Container>
                <div className='w-full max-w-[553px]'>
                    <PageDescription
                        title={title}
                        description='پلاک خود را وارد کرده تا از وضعیت پرداختی‌های خود مطلع شوید.'
                    />
                    <InsuranceFormContainer isMotor={isMotor} status={status}/>
                </div>
            </Container>

        </>
    );
}
