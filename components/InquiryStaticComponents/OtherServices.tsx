import React from 'react'
import RoundedArrow from '../common/svg/RoundedArrow'
import Link from 'next/link'
// import Image from 'next/image'
// import ServiceItem from "@/public/assets/images/service-item.png"
import { CircleMinus, IdCard } from 'lucide-react'
import CarAccident from "../common/svg/CarAccident"
import LicensePlate from '../common/svg/LicensePlateIcon'
import RoadIcon from '../common/svg/RoadIcon'
const OtherServices = ({className} : {className?: string}) => {
    return (
        <section className={`w-[95%] mx-auto max-w-6xl bg-white md:p-8 p-2 rounded-3xl shadow-lg overflow-hidden ${className}`}>
            <div className='mx-auto w-fit flex gap-1 mb-16 mt-5'>
                <h3
                    className="title-with-pluses text-xl md:text-2xl whitespace-nowrap"

                >سرویس های مشابه دیگر ما</h3>
                <RoundedArrow />
            </div>
            <div className='flex md:justify-between max-md:overflow-x-auto max-md:overflow-y-hidden mx-auto my-6 w-10/12 gap-5 max-md:gap-7'>
                <Link href="" className='flex flex-col justify-center items-center w-28 '>
                    <div className='md:w-full md:h-28  align-middle text-center flex justify-center items-center rounded-full bg-[#6BE990] text-white p-2'>
                        <CarAccident className='w-16 h-16 ' />
                    </div>
                        <h3 className='md:text-sm text-xs mt-2 whitespace-nowrap'> استعلام بیمه شخص ثالث </h3>
                </Link>
                <Link href="" className='flex flex-col justify-center items-center md:w-28'>
                    <div className='md:w-full md:h-28 align-middle text-center flex justify-center items-center rounded-full bg-[#32acfe] text-white p-2'>
                        <IdCard className='w-16 h-16'/>
                    </div>
                        <h3 className='md:text-sm text-xs mt-2 whitespace-nowrap'>وضعیت کارت و سند خودرو</h3>
                </Link>
                <Link href="" className='flex flex-col justify-center items-center md:w-28'>
                    <div className='md:w-full md:h-28 align-middle text-center flex justify-center items-center rounded-full bg-[#FF6D4A] text-white p-2'>
                        <CircleMinus className='w-16 h-16' />
                    </div>
                        <h3 className='md:text-sm text-xs mt-2 whitespace-nowrap'> استعلام نمره منفی </h3>
                </Link>
                <Link href="" className='flex flex-col justify-center items-center md:w-28'>
                    <div className='md:w-full md:h-28 align-middle text-center flex justify-center items-center rounded-full bg-[#7495D1] text-white p-2'>
                        <LicensePlate className='w-16 h-16' fill="#fff" />
                    </div>
                        <h3 className='md:text-sm text-xs mt-2 whitespace-nowrap'> استعلام تاریخچه پلاک  </h3>
                </Link>
                <Link href="" className='flex flex-col justify-center items-center md:w-28'>
                    <div className='md:w-full md:h-28 align-middle text-center flex justify-center items-center rounded-full bg-[#596068] text-white p-2'>
                        <RoadIcon className='w-16 h-16' fill="#fff" />
                    </div>
                        <h3 className='md:text-sm text-xs mt-2 whitespace-nowrap'> استعلام عوارض آزاد راه   </h3>
                </Link>
                                        
                {/* <Link href={"#"}>
                    <div className='md:max-w-40'>
                        <Image src={ServiceItem} className='w-32' alt='service-item' />
                        <h3 className='text-sm mt-2 whitespace-nowrap'>وضعیت کارت و سند خودرو</h3>
                    </div>
                </Link>
                <Link href={"#"}>
                    <div className='md:max-w-40'>
                        <Image src={ServiceItem} className='w-32' alt='service-item' />
                        <h3 className='text-sm mt-2 whitespace-nowrap'>وضعیت کارت و سند خودرو</h3>
                    </div>
                </Link>
                <Link href={"#"}>
                    <div className='md:max-w-40'>
                        <Image src={ServiceItem} className='w-32' alt='service-item' />
                        <h3 className='text-sm mt-2 whitespace-nowrap'>وضعیت کارت و سند خودرو</h3>
                    </div>
                </Link> */}
            </div>
        </section>
    )
}

export default OtherServices