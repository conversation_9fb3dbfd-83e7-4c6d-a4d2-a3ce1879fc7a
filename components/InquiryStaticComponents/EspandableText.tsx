"use client"
import {useState, ReactNode} from "react";
import Arrow<PERSON><PERSON> from "../common/svg/ArrowBack";

export default function ExpandableText({children}: { children: ReactNode }) {
    const [expanded, setExpanded] = useState(false);

    return (
        <div className="relative text-bg w-full mt-5 mx-auto text-center">
            {/* Text Container */}
            <div className={`relative overflow-hidden transition-all ${expanded ? "max-h-none" : "max-h-32"}`}>
                <p className="text-gray-700 leading-8 text-justify">
                    {children}
                </p>
                {!expanded && (
                    <div
                        className="absolute top-9 bottom-0 left-0 w-full bg-gradient-to-t from-gray-100 to-transparent"></div>
                )}
            </div>

            {/* Show More CustomButton */}
            <button
                onClick={() => setExpanded(!expanded)}
                className="mt-3 px-8 py-2 bg-blue-500 text-white rounded-lg"
            >
        <span className="flex gap-5 items-center">
        {expanded ? "نمایش کمتر" : "نمایش بیشتر"}
            <ArrowBack className="size-3"/>
        </span>
            </button>
        </div>
    );
}
