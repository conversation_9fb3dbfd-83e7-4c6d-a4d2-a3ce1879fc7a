'use client'

import { useState, useEffect } from 'react'
import { Attribute, AttributeValue } from '@/lib/types/product.types'
import {cn} from "@/lib/utils";

/**
 * SelectColor component allows users to select a color from available options
 *
 * @param {Object} props - Component props
 * @param {Attribute[]} props.attributes - Array of product attributes
 * @param {AttributeValue} [props.defaultValue] - Optional default selected color
 * @param {Function} [props.onChange] - Optional callback when color selection changes
 */
const SelectColor = ({
    attributes,
    defaultValue = null,
    onChange
}: {
    attributes: Attribute[],
    defaultValue?: AttributeValue | null,
    onChange?: (selectedColor: AttributeValue | null) => void
}) => {
    // State to track the selected color
    const [selectedColor, setSelectedColor] = useState<AttributeValue | null>(defaultValue)

    // Update selected color when defaultValue changes
    useEffect(() => {
        if (defaultValue !== null) {
            setSelectedColor(defaultValue)
        }
    }, [defaultValue])

    // Handle color selection
    const handleColorSelect = (color: AttributeValue) => {
        const newSelection = selectedColor?.value === color.value ? null : color
        setSelectedColor(newSelection)
        

        // Call the onChange callback if provided
        if (onChange) {
            onChange(newSelection)
        }
    }

    return (
        <div className="flex gap-6 max-md:w-max md:w-full md:flex-wrap md:pb-4">
            {attributes
                .filter(attr => attr.type === 'color')
                .map(attr => (
                    <div key={attr.type} className="flex gap-4">
                        {attr.values.map((color) => (
                            <div
                                key={color.value}
                                className="flex flex-col gap-2 items-center justify-center cursor-pointer"
                                onClick={() => handleColorSelect(color)}
                            >
                                <div
                                    className={`border-2  rounded-full p-1 transition-all duration-200 ${
                                        selectedColor?.value === color.value
                                            ? 'border-gray-500 scale-110'
                                            : 'hover:border-gray-400 border-dashed'
                                    }`}
                                >
                                    <div
                                        className="w-10 h-10 rounded-full relative"
                                        style={{
                                            backgroundImage: `linear-gradient(to right, ${color.extra_data?.hex || '#ccc'}, transparent)`
                                        }}
                                    >
                                        {/* {selectedColor?.value === color.value && (
                                            <div className="absolute inset-0 flex items-center justify-center">
                                                <div className="w-4 h-4 bg-white rounded-full flex items-center justify-center">
                                                    <div className="w-2 h-2 bg-[#2DC058] rounded-full"></div>
                                                </div>
                                            </div>
                                        )} */}
                                    </div>
                                </div>
                                <span className={cn(
                                    selectedColor?.value === color.value && "font-semibold"
                                )}>
                                    {color.value}
                                </span>
                            </div>
                        ))}
                    </div>
                ))}
        </div>
    )
}

export default SelectColor