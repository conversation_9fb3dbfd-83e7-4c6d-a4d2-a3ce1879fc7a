'use client';

import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Thumbs } from 'swiper/modules';
import { useState } from 'react';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/thumbs';
import Image from 'next/image';
import type { Swiper as SwiperType } from 'swiper';
import { Gallery } from '@/lib/types/product.types';
import EmptyPic from "@/public/assets/images/empty-cover.webp" 
export default function ProductGallery({ images }: { images: Gallery[] }) {
  const [thumbsSwiper, setThumbsSwiper] = useState<SwiperType | null>(null);
  const [mainSwiperReady, setMainSwiperReady] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalStartIndex, setModalStartIndex] = useState(0);
  const [modalThumbsSwiper, setModalThumbsSwiper] = useState<SwiperType | null>(null);

  const openModal = (startIndex: number) => {
    setModalStartIndex(startIndex);
    setIsModalOpen(true);
  };
  const closeModal = () => {
    setIsModalOpen(false);
    setModalThumbsSwiper(null);
  };

  return (
    <div className="md:p-6">
      <div className="relative rounded-2xl bg-[#F9FAFB]">
        {/* Discount badge */}
        {/* <div className="absolute top-2 left-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full z-10">
          10%
        </div> */}

        {/* Main gallery wrapper با ارتفاع ثابت */}
        <div className="h-[400px] max-md:h-[250px] overflow-hidden rounded-xl relative">
          {/* Spinner */}
          {!mainSwiperReady && (
            <div className="absolute inset-0 flex justify-center items-center">
              <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-gray-900"></div>
            </div>
          )}

          <Swiper
            modules={[Thumbs]}
            thumbs={{ swiper: thumbsSwiper }}
            spaceBetween={10}
            slidesPerView={1}
            onInit={() => setMainSwiperReady(true)}
            className={`${!mainSwiperReady ? 'hidden' : ''} h-full`}
          >
            {images.slice(0, 3).map((img, i) => (
              <SwiperSlide
                key={i}
                className="relative h-full border-2 border-dashed cursor-pointer"
                onClick={() => openModal(i)}
              >
                <Image
                  src={img.url || EmptyPic}
                  alt={img.caption}
                  fill
                  className="object-contain rounded-xl"
                  priority={i === 0}
                />
              </SwiperSlide>
            ))}
          </Swiper>
        </div>

        {/* Thumbnails wrapper با ارتفاع مشخص */}
        {mainSwiperReady && (
          <div className="mt-4 h-24 max-md:hidden">
            <Swiper
              modules={[Navigation, Thumbs]}
              onSwiper={setThumbsSwiper}
              spaceBetween={10}
              slidesPerView={4}
              watchSlidesProgress
              className="h-full"
            >
              {images.slice(0, 3).map((img, i) => (
                <SwiperSlide
                  key={i}
                  className="relative h-full rounded-lg bg-white p-1 cursor-pointer"
                  onClick={() => openModal(i)}
                >
                  <Image
                    src={img.url || EmptyPic}
                    alt={`Thumbnail ${img.caption}`}
                    fill
                    className="object-cover rounded"
                  />
                </SwiperSlide>
              ))}
              {images.length > 3 && (
                <SwiperSlide
                  className="relative h-full rounded-lg bg-gray-200 cursor-pointer"
                  onClick={() => openModal(3)}
                >
                  <div className="absolute inset-0 flex items-center justify-center text-gray-600 text-lg font-semibold">
                    +{images.length - 3}
                  </div>
                </SwiperSlide>
              )}
            </Swiper>
          </div>
        )}
      </div>

      {/* Modal */}
      {isModalOpen && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60"
          onClick={closeModal}
        >
          <div
            className="relative w-full max-w-2xl mx-4 bg-white rounded-xl overflow-hidden"
            onClick={e => e.stopPropagation()}
          >
            {/* Close */}
            <button
              onClick={closeModal}
              className="absolute top-2 right-2 text-black text-2xl font-bold z-10"
            >
              &times;
            </button>

            {/* Main modal Swiper با ارتفاع vh */}
            <div className="h-[50vh] relative w-[96%] mx-auto !rounded-xl">
              <Swiper
                modules={[Navigation, Thumbs]}
                initialSlide={modalStartIndex}
                navigation
                thumbs={{ swiper: modalThumbsSwiper }}
                spaceBetween={20}
                slidesPerView={1}
                className="h-full"
              >
                {images.map((img, idx) => (
                  <SwiperSlide key={idx} className="relative h-full mt-10 flex items-center justify-center ">
                    <Image
                      src={img.url || EmptyPic}
                      alt={img.caption}
                      fill
                      className="!rounded-xl"
                    />
                  </SwiperSlide>
                ))}
              </Swiper>
            </div>

            {/* Modal thumbnails با ارتفاع ثابت */}
            <div className="mt-4 pb-4 h-24 w-[96%] mx-auto !rounded-xl">
              <Swiper
                modules={[Thumbs]}
                onSwiper={setModalThumbsSwiper}
                spaceBetween={10}
                slidesPerView={6}
                watchSlidesProgress
                className="h-full"
              >
                {images.map((img, idx) => (
                  <SwiperSlide key={idx} className="relative h-full rounded-lg p-1 cursor-pointer">
                    <Image
                      src={img.url || EmptyPic}
                      alt={img.caption}
                      fill
                      className="object-cover rounded"
                    />
                  </SwiperSlide>
                ))}
              </Swiper>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
