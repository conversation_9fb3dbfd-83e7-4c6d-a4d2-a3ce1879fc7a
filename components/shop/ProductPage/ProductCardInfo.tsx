'use client'

import { LayoutGrid } from 'lucide-react';
import { AttributeValue, ProductData, Variation } from '@/lib/types/product.types';
import SelectColor from './SelectColor';
import SelectAttribute from './SelectSize';



interface ProductCardInfoProps extends ProductData {
    onChange?: (color: AttributeValue | null) => void;
    onSizeChange?: (size: string | null) => void;
    selectedColor?: AttributeValue | null;
    selectedSize?: string | null;
    selectedVariant: Variation | null
}

const ProductCardInfo = ({
    details,
    attributes,
    category,
    variants,
    description,
    onChange,
    onSizeChange,
    selectedColor,
    selectedSize,
    selectedVariant
}: ProductCardInfoProps) => {
console.warn(selectedColor);


    const handleColorChange = (color: AttributeValue | null) => {
        if (onChange) {
            onChange(color);
        }
    };


    const handleSizeChange = (size: string | null) => {

        if (onSizeChange) {
            onSizeChange(size);
        }
    };
    return (
        <div className='max-md:flex max-md:flex-col'>
            <div className='flex flex-wrap product-details justify-between text-sm order-2'>
                {details.map((detail) => (
                    <div key={detail.key}>
                        <h3>{detail.key}</h3>
                        <span>{detail.value}</span>
                    </div>
                ))}
            </div>

            <div className=' order-1'>
                <div>
                    {
                        // checking if color type exists then show it
                        attributes.filter(attr => attr.type === 'color').length ?
                        <div>
                            
                            <h3 className='my-2 max-md:mt-2 flex items-center gap-2'>
                                رنگ انتخابی شما
                                {/* {selectedColor && (
                                    <span className="flex items-center gap-1 bg-gray-100 px-2 py-1 rounded-full text-sm">
                                        <span
                                            className="inline-block w-3 h-3 rounded-full"
                                            style={{
                                                backgroundColor: selectedColor.extra_data?.hex || '#ccc'
                                            }}
                                        ></span>
                                        {selectedColor.value}
                                    </span>
                                )} */}
                            </h3>
                            <SelectColor
                                attributes={attributes}
                                defaultValue={selectedColor}
                                onChange={handleColorChange}
                            />

                        </div>
                        :
                        ""
                    }

                    {/* Dynamic Secondary Attribute Selection */}
                    {attributes.filter(attr => attr.type !== 'color' && attr.values.length > 0).map((attribute) => {
                        // Check if this product has color attributes
                        const hasColorAttributes = attributes.some(attr => attr.type === 'color' && attr.values.length > 0);

                        // Only disable if there are color attributes AND no color is selected
                        const shouldDisable = hasColorAttributes && !selectedColor;

                        return (
                            <div key={attribute.type} className="mt-4">
                                <h3 className='my-2 max-md:mt-2 flex items-center gap-2'>
                                    {attribute.title}
                                    {/* {selectedSize && (
                                        <span className="flex items-center gap-1 bg-gray-100 px-2 py-1 rounded-full text-sm">
                                            {selectedSize}
                                        </span>
                                    )} */}
                                </h3>

                                <SelectAttribute
                                    attributes={attributes}
                                    variants={variants}
                                    selectedColor={selectedColor}
                                    defaultValue={selectedSize}
                                    onChange={handleSizeChange}
                                    disabled={shouldDisable}
                                    selectedVariant={selectedVariant}
                                    attributeType={attribute.type}
                                    attributeTitle={attribute.title}
                                    hasColorDependency={hasColorAttributes}
                                />
                            </div>
                        );
                    })}
                </div>

                <div className='category text-[#9DA5B0] flex md:flex-col items-start gap-5 mb-5 text-sm max-md:mt-5'>
                    <span className='flex gap-2'><LayoutGrid /> دسته بندی</span>
                    <div className='flex justify-between gap-3 flex-wrap'>
                        
                            <span className='bg-[#F9FAFB] border-2 border-[#F1F1F6] px-2.5 py-1 rounded-3xl whitespace-nowrap'>
                                {category.title}
                            </span>
                        
                    </div>
                </div>

                <div>
                    <p className='text-justify leading-8 text-sm'>
                        {description}
                    </p>
                </div>
            </div>
            {/* <ProviderShopInfo /> */}
        </div>
    )
}

export default ProductCardInfo