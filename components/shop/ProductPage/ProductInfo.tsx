import React from 'react'
import AccordionWrapper from './AccordionWrapper'
import BoxDone from '@/components/common/svg/BoxDone'
import ProductInfoItem from './ProductInfoItem'
import { Detail } from '@/lib/types/product.types'

const ProductInfo = ({productDetails}: {productDetails: Detail[]}) => {
  return (
    <section className='bg-white mt-10 md:p-5 max-md:p-3 rounded-3xl scroll-mt-28' id='productInfo'>
        <AccordionWrapper title='مشخصات محصول 'icon={<BoxDone />} >
        {
          productDetails.length && productDetails.map((item, index) => <ProductInfoItem detail={item} key={index} />)
        }
            
            {/* <ProductInfoItem />
            <ProductInfoItem />
            <ProductInfoItem />
            <ProductInfoItem /> */}
        </AccordionWrapper>
    </section>
  )
}

export default ProductInfo