
const ProviderShopInfoSkeleton = () => {
  return (
     <div className="bg-[#F5F6F8] h-auto md:p-6 max-md:p-3 rounded-3xl md:w-[27%] animate-pulse">
                {/* Header Skeleton */}
                <div className="flex justify-between border-b-2 pb-4 max-md:px-2 max-md:py-3">
                    <div className="h-4 w-20 bg-gray-300 rounded"></div>
                    <div className="h-4 w-24 bg-gray-300 rounded"></div>
                </div>

                {/* Seller Info Skeleton */}
                <div className="mt-5 pb-2 border-b-2 border-dashed">
                    <div className="flex items-center gap-2 mb-3">
                        <div className="w-5 h-5 bg-gray-300 rounded-full" />
                        <div className="h-4 w-32 bg-gray-300 rounded"></div>
                    </div>
                    <div className="flex gap-2 mb-4">
                        <div className="h-4 w-24 bg-gray-300 rounded"></div>
                        <span> | </span>
                        <div className="h-4 w-16 bg-gray-300 rounded"></div>
                    </div>
                </div>

                {/* Price and Stock Info Skeleton */}
                <div className="mt-5">
                    <div className="h-6 w-36 bg-gray-300 rounded mb-2"></div>
                    <div className="h-5 w-28 bg-gray-300 rounded mb-4"></div>
                    <div className="h-4 w-40 bg-gray-300 rounded"></div>
                </div>

                {/* Counter Placeholder */}
                <div className="h-10 bg-gray-300 rounded-lg mt-4"></div>

                {/* Guarantees and Delivery Skeleton */}
                <div className="mt-5 max-md:flex max-md:justify-between max-md:text-xs max-md:gap-1">
                    <div className="bg-white rounded-3xl my-3 p-3 flex items-center gap-2 w-full">
                        <div className="w-5 h-5 bg-gray-300 rounded-full"></div>
                        <div className="h-4 w-20 bg-gray-300 rounded"></div>
                    </div>
                    <div className="bg-white rounded-3xl my-3 p-3 flex items-center gap-2 w-full">
                        <div className="w-5 h-5 bg-gray-300 rounded-full"></div>
                        <div className="h-4 w-28 bg-gray-300 rounded"></div>
                    </div>
                </div>
            </div>

  )
}

export default ProviderShopInfoSkeleton