'use client';

import { Dialog, DialogContent, DialogTitle, DialogClose } from '@/components/UI/dialog';
import { CheckCircle2, X } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect } from 'react';

interface CartConfirmationModalProps {
  open: boolean;
  onClose: () => void;
  productInfo: {
    image?: string;
    name?: string;
    color?: string;
    details?: Array<{
      title: string;
      value: string | number;
      extra: {
        [key: string]: string | number | undefined;
      } | null;
    }>;
  };
}

const CartConfirmationModal = ({ open, onClose, productInfo }: CartConfirmationModalProps) => {
  // console.log(productInfo);
  

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="md:!w-[525px] max-md:w-[95%] p-0 gap-0 rounded-2xl [&>button]:hidden">
        <div className="p-4 relative">
          <DialogTitle className="sr-only">افزودن به سبد خرید</DialogTitle>

          <div className='mb-5'>
            {/* Single Close Button at top-left (RTL) */}
            <DialogClose className="absolute left-4 top-4 rounded-full p-1.5 border-2 border-gray-300 hover:bg-gray-100 transition-colors">
              <X className="h-5 w-5 text-gray-500" />
            </DialogClose>

            <div className="flex items-center gap-2 text-primary mb-4 absolute right-4 top-4">
              <div className="bg-primary/10 rounded-full p-1">
                <CheckCircle2 className="h-6 w-6" />
              </div>
              <span className="font-semibold">محصول به سبد خرید اضافه شد</span>
            </div>
          </div>

          <div className="flex items-center gap-4 pt-6">
            {productInfo.image && (
              <div className="relative w-24 h-24 border border-gray-100 rounded-2xl overflow-hidden bg-gray-50">
                <Image
                  fill
                  src={productInfo.image}
                  alt={productInfo.name || ''}
                  className="object-cover"
                />
              </div>
            )}
            <div className="flex-1">
              <h3 className="font-semibold mb-2 text-gray-900">{productInfo.name}</h3>
              {/* {productInfo.color && (
                <p className="text-sm text-gray-500">رنگ: {productInfo.color}</p>
              )} */}
              {
                productInfo.details?.map(detail => (
                  <p key={detail.title} className="text-sm text-gray-500"><span>{detail.title}</span>: {detail.value}</p>
                ))

              }
            </div>
          </div>
        </div>

        <div className="flex border-t border-gray-100 overflow-hidden px-4 mb-3">
          {/* <button
            onClick={onClose}
            className="flex-1 p-4 text-gray-600 hover:bg-gray-50 transition-colors font-medium"
          >
            ادامه خرید
          </button> */}
          <Link
            href="/checkout/cart"
            className="flex-1 rounded-lg p-4 overflow-hidden text-center bg-primary text-white hover:bg-primary/90 transition-colors font-medium"
          >
            مشاهده سبد خرید
          </Link>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CartConfirmationModal;
