import Image from "next/image"
import AccordionWrapper from "./AccordionWrapper"
import Motoroil from "@/public/assets/images/motor-oil.png"
import { Info } from "lucide-react"
const ProductDescription = () => {
  return (
    <section className="bg-white p-3 rounded-3xl scroll-mt-28" id="productDescription">
        <AccordionWrapper title="توضیحات محصول" icon={<Info size={25} />} >
            <div className="text-sm">
                <p className="text-justify leading-8 mb-5">
                لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ و با استفاده از طراحان گرافیک است. چاپگرها و متون بلکه روزنامه و مجله در ستون و سطرآنچنان که لازم است و برای شرایط فعلی تکنولوژی مورد نیاز و کاربردهای متنوع با هدف بهبود ابزارهای کاربردی می باشد. کتابهای زیادی در شصت و سه درصد گذشته، حال و آینده شناخت فراوان جامعه و متخصصان را می طلبد تا با نرم افزارها شناخت بیشتری را برای طراحان رایانه ای علی الخصوص طراحان خلاقی و فرهنگ پیشرو در زبان فارسی ایجاد کرد. 
                </p>
                <Image className="w-full" src={Motoroil} alt="" />
                <p className="my-5 text-justify leading-8">
                لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ و با استفاده از طراحان گرافیک است. چاپگرها و متون بلکه روزنامه و مجله در ستون و سطرآنچنان که لازم است و برای شرایط فعلی تکنولوژی مورد نیاز و کاربردهای متنوع با هدف بهبود ابزارهای کاربردی می باشد. کتابهای زیادی در شصت و سه درصد گذشته، حال و آینده شناخت فراوان جامعه و متخصصان را می طلبد تا با نرم افزارها شناخت بیشتری را برای طراحان رایانه ای علی الخصوص طراحان خلاقی و فرهنگ پیشرو در زبان فارسی ایجاد کرد. 

                </p>
            </div>
        </AccordionWrapper> 
    </section>
  )
}

export default ProductDescription