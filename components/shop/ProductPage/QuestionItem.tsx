import { Question } from '@/lib/types/product.types'
import { CircleHelp, Info, ThumbsUp, ThumbsDown } from 'lucide-react'
import AnswerItem from './AnswerItem'

const QuestionItem = ({ question }: { question: Question }) => {
    return (
        <div key={question.id} className="bg-[#F5F6F8] rounded-3xl md:p-3 mt-5 py-5">
            <div className="commets-header flex justify-between md:w-[88%] max-md:w-full mx-auto pt-10 max-md:px-2">
                <div className="right flex items-center md:gap-5 max-md:gap-2 md:w-[45%]">
                    <div className='md:max-w-[23%]'>
                        <div className="md:mx-3">
                            <div className="p-2 rounded-full bg-[#637382] ">
                                <CircleHelp color="white" size={32} />
                            </div>
                        </div>
                    </div>
                    <div className="flex flex-col">
                        <h3>
                            {question.user.name}
                        </h3>
                        <div className="flex items-center text-sm max-md:text-xs h-7 gap-2">
                            <Info size={18} />
                            <span className="">
                                {question.created_at}
                            </span>
                            <span>

                            </span>
                        </div>
                    </div>
                </div>
                <div className="left flex items-center gap-5 bg-gradient-to-l from-white to-transparent md:p-2.5 max-md:p-1.5 rounded-3xl">
                    <div className="flex items-center gap-5 border md:px-3 py-2 max-md:p-3 rounded-3xl text-gray-400">
                        <p className="flex items-center text-sm md:gap-2">
                            <span>
                                0
                            </span>
                            <span>
                                <ThumbsUp size={24} className=' max-md:w-5' />
                            </span>
                        </p>
                        <p className="flex items-center text-sm gap-2">
                            <span>
                                0
                            </span>
                            <span>
                                <ThumbsDown size={24} className=' max-md:w-5' />
                            </span>
                        </p>

                    </div>

                </div>

            </div>
            <div className="md:px-5 max-md:px-2 pb-12">
                <div className="bg-white rounded-2xl p-4 mb-7 mt-5 max-md:text-sm max-md:leading-7">
                    <p>
                        {question.body}
                    </p>
                </div>
                {
                    question.answers.length && question.answers.map(answer => (
                        <AnswerItem key={answer.id} answer={answer} />

                    ))
                }
            </div>
        </div>
    )
}

export default QuestionItem