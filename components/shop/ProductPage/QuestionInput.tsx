'use client';

import { useState } from 'react';

export default function QuestionInput() {
  const [question, setQuestion] = useState('');

  const handleSubmit = () => {
    // Handle submit logic here
    console.log(question);
  };

  return (
    <div className="w-full ">
      <div className="flex items-center gap-2 rounded-2xl border border-gray-200 bg-white overflow-hidden shadow-sm p-4">
        <textarea
          
          dir="rtl"
          value={question}
          onChange={(e) => setQuestion(e.target.value)}
          placeholder="سوال خود را بنویسید"
          className="flex-1 px-4 py-3 text-gray-800 placeholder-gray-400 text-sm outline-none bg-transparent"
        />
        <button
          onClick={handleSubmit}
          className="bg-gray-100 text-gray-500 text-sm px-6 py-3 rounded-xl hover:bg-gray-200 transition-all"
        >
          ارسال سوال
        </button>
      </div>
    </div>
  );
}
