import { Answer } from "@/lib/types/product.types"
import { UserRound } from "lucide-react"

const AnswerItem = ({answer}: {answer: Answer}) => {
    return (
        <div key={answer.id} className="bg-[#637382] my-6 text-white p-5 rounded-3xl md:w-[90%] mr-auto max-md:text-sm max-md:leading-7">
            <p className="leading-8">
                {answer.body}
            </p>
            <div className="mt-8 flex gap-3 items-center">
                <span className="flex items-center gap-2">
                    <UserRound /> صمد سپهری
                </span>
                <span> | </span>
                <span>
                    {answer.created_at.split(" ")[0]} {/* Date: 2025-05-19 */}
                </span>
                <span> | </span>
                <span> {answer.created_at.split(" ")[1]} {/* Time: 09:33:51 */} </span>
            </div>
        </div>
    )
}

export default AnswerItem