import { Star } from "lucide-react";

const RatingStars = ({ rate }: { rate: number }) => {
  const clampedRate = Math.min(Math.max(0, rate), 5);
  const fullStars = Math.floor(clampedRate);
  const hasHalfStar = clampedRate % 1 >= 0.5;
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

  return (
    <div className="flex gap-1 items-center h-full">
      {Array(fullStars).fill(0).map((_, i) => (
        <Star key={`full-${i}`} className="w-4 text-[#F7BC06]" fill="#F7BC06" />
      ))}
      
      {hasHalfStar && (
        <div className="relative w-4 h-4">
          <Star className="absolute w-4 text-[#9DA5B0]" fill="#9DA5B0" />
          <div className="absolute w-2 overflow-hidden">
            <Star className="w-4 text-[#F7BC06]" fill="#F7BC06" />
          </div>
        </div>
      )}
      
      {Array(emptyStars).fill(0).map((_, i) => (
        <Star key={`empty-${i}`} className="w-4 text-[#9DA5B0]" fill="#9DA5B0" />
      ))}
    </div>
  );
};

export default RatingStars