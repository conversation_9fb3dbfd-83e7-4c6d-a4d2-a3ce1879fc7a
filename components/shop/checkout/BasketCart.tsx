"use client"
import { useState, useEffect } from "react"
import <PERSON><PERSON><PERSON>heck from "@/components/common/svg/BasketCheck"
import { ArrowRight, CircleChevronRight, Loader2 } from "lucide-react"
import { useCart } from "@/lib/context/cart-context"
import BasketCartItem from "./BasketCartItem"
import CheckoutProgress from "./CheckoutProgress"


const BasketCart = () => {
  const { cartItems, isLoading, isUserContextInitialized, isInitialized, reloadCart } = useCart();
  const [showContent, setShowContent] = useState<'loading' | 'items' | 'empty'>('loading');


useEffect(() => {
  console.log("User initialized:", isUserContextInitialized);
  console.log("Cart initialized:", isInitialized);

  if (isUserContextInitialized && !isInitialized) {
    console.log("⏳ Reloading cart...");
    reloadCart();
    
  }
}, [isUserContextInitialized, isInitialized]);


  // Determine what to show based on the state of both contexts
  useEffect(() => {
    // If user context is not initialized yet, keep showing loading
    console.log(cartItems);
    console.log("rendred");
    if (!isUserContextInitialized) {
      setShowContent('loading');
      return;
    }

    // If cart is still loading, show loading
    if (isLoading) {
      setShowContent('loading');
      return;
    }

    // If cart is loaded and has items, show items
    if (cartItems.length > 0) {
      setShowContent('items');
      return;
    }

    // If cart is loaded but empty, show empty message
    setShowContent('empty');
    
  }, [isLoading, isUserContextInitialized, isInitialized, cartItems.length]);

  const itemCount = showContent === 'items' ? cartItems.length : 0;

  return (
    <div className='max-md:w-full max-md:mx-auto bg-white rounded-3xl p-8 max-md:p-5 md:min-h-[30rem] max-md:min-h-80 '>
      <CheckoutProgress
          steps={[
            { title: 'سبد خرید', status: 'current' },
            { title: 'انتخاب آدرس', status: 'upcoming' },
            { title: 'پرداخت', status: 'upcoming' },
          ]}
        />
      <div className="header flex justify-between">
        <div className='flex flex-col gap-3 max-md:gap-2 relative pb-5 title-bt-border'>
          <h2 className='text-xl font-black max-md:text-base flex gap-2 items-center'>
          <ArrowRight size={24} className='opacity-20 cursor-not-allowed md:hidden' />  سبد خرید  
          </h2>
          <h3 className='text-gray-500 max-md:text-sm'>
            {itemCount} محصول در سبد موجود است
          </h3>
        </div>
        <div>
          <BasketCheck className='max-md:w-12' size='70' />
        </div>
      </div>

      {showContent === 'loading' && (
        <div className="w-full h-full flex justify-center items-center pt-20">
          <Loader2 className="animate-spin" size={24} />
        </div>
      )}

      {showContent === 'items' && (
        <div>
          {cartItems.map(item => <BasketCartItem key={item.id} product={item} />)}
        </div>
      )}

      {showContent === 'empty' && (
        <div className='text-center py-10'>
          <h4 className='text-lg font-semibold'>سبد خرید شما خالی است</h4>
        </div>
      )}
    </div>
  );
}

export default BasketCart