"use client"
import { UserAddress } from "@/lib/types/types";
import { Edit, Trash2 } from "lucide-react";
import { useEffect, useRef, useState } from "react";

// interface Address {
//     id: string
//     address: string
//     city: string
//     province: string
// }

interface AddressDialogProps {
    open: boolean
    onClose: () => void
    addressList: UserAddress[]
    onEdit: (address: UserAddress) => void
    onDelete: (id: string) => void
}
const ManageAddressesModal = ({ open, onClose, addressList, onDelete, onEdit }: AddressDialogProps) => {
    const modalRef = useRef<HTMLDivElement>(null); // Ref for the modal content
    const [loadingId, setLoadingId] = useState<string | null>(null); // Track loading state for delete

    // Close modal when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
                onClose();
            }
        };

        if (open) {
            document.addEventListener("mousedown", handleClickOutside);
        }

        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [open, onClose]);

    const handleDelete = async (id: string) => {
        setLoadingId(id);
        await onDelete(id);
        setLoadingId(null);
    };
    const handleEditAddress = (id: string) => {
        const addressToEdit = addressList.find(item => item.id === id);
        if (addressToEdit) {
            onEdit(addressToEdit); // Pass the whole address object
        }
    };

    if (!open) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
            <div
                ref={modalRef}
                className="bg-white md:w-[800px] rounded-lg p-6 md:h-[500px] max-md:max-w-[95%] max-md:h-[400px] overflow-y-auto relative"
                dir="rtl"
                style={{ direction: 'ltr' }}  // Force scrollbar to right
            >
                <div className="flex justify-between items-start mb-5">
                    <h2 className="text-lg font-bold mb-4 p-0">مدیریت آدرس ها</h2>
                    <button
                        onClick={onClose}
                        className=" text-gray-500 hover:text-gray-700"
                    >
                        ✕
                    </button>
                </div>
                <ul className="flex flex-col gap-5">
                    {addressList.map((item) => (
                        <li key={item.id} className="flex justify-between items-center border-b pb-2">
                            <div className="text-right"> {/* RTL text alignment */}
                                <p className="text-base font-medium">{item.address}</p>
                                <p className="text-xs text-gray-500">{item.city}, {item.province}</p>
                            </div>
                            <div className="flex gap-2">
                                <button onClick={() => handleEditAddress(item.id)} className="text-blue-500 hover:text-blue-700">
                                    <Edit size={18} />
                                </button>
                                <button
                                    onClick={() => handleDelete(item.id)}
                                    className="text-red-500 hover:text-red-700 flex items-center gap-2"
                                    disabled={loadingId === item.id}
                                >
                                    {loadingId === item.id ? (
                                        <span className="loader border-t-transparent border-4 border-red-500 rounded-full w-4 h-4 animate-spin"></span>
                                    ) : (
                                        <Trash2 size={18} />
                                    )}
                                </button>
                            </div>
                        </li>
                    ))}
                </ul>
            </div>
        </div>
    );
};

export default ManageAddressesModal;