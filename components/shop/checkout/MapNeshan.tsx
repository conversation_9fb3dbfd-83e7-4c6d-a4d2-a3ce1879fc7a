"use client"
import {useEffect, useRef} from "react"
import {Map} from "@neshan-maps-platform/ol"
import NeshanMap, {NeshanMapRef} from "@neshan-maps-platform/react-openlayers"


function MapNeshan() {
 return <NeshanMap
       mapKey="web.9617d3844fe74e8eba579b02a2320354"
       center={{ latitude: 35.7665394, longitude: 51.4749824 }}
       className="w-[500px] h-[500px]"
       zoom={14}
></NeshanMap>
}


export default MapNeshan