'use client'

import CustomButton from '@/components/UI/CustomButton'
import { useCart } from '@/lib/context/cart-context'
import { useAuth } from '@/lib/hooks/useAuth'
import { useRouter } from 'nextjs-toploader/app'
import toast from 'react-hot-toast'

type FactorCardProps = {
  paymentMethod?: string;
  steps: {
    nextStepBtnTitle: string;
    nextStepBtnLink: string;
    title: string;
  };
  onCreateInvoice?: () => void;
  selectedAddress?: string;
  isNavigating: boolean;
  setIsNavigating: (load: boolean) => void;
  deliveryId?: string;
  disabled?: boolean;
  showCompleteProfile?: boolean;
}

const FactorCard = ({ paymentMethod = 'wallet', steps, onCreateInvoice, selectedAddress, isNavigating, setIsNavigating, deliveryId, disabled, showCompleteProfile }: FactorCardProps) => {
  const { finalPrice, totalDiscount, totalPrice, totalItems, cartItems, deliveryPrice } = useCart()
  const { userData } = useAuth()
  const router = useRouter()

  const handleNextStep = async () => {
    if (steps.title === "shipping" && !selectedAddress) {
      toast.error("لطفا آدرس خود را انتخاب یا وارد کنید")
      return
    }
    if (steps.title === "shipping" && !deliveryId) {
      toast.error("لطفا روش ارسال را انتخاب کنید")
      return
    }
    if (steps.title === "cart" && totalItems === 0) {
      toast.error("لطفا کالایی را به سبد خرید اضافه کنید")
      return
    }
    setIsNavigating(true)
    router.push(steps.nextStepBtnLink)
  }
  console.log(userData?.balance, finalPrice);
  console.log(typeof (userData?.balance), typeof (finalPrice));



  function normalizeToNumber(value: unknown): number {
    if (typeof value === 'number') return value;
    if (typeof value === 'string') {
      const numeric = Number(value.replace(/,/g, ''));
      return isNaN(numeric) ? 0 : numeric;
    }
    return 0;
  }

  function getPayableAfterWallet(finalPrice: unknown, walletBalance: unknown): string {
    const price = normalizeToNumber(finalPrice);
    const balance = normalizeToNumber(walletBalance);

    const remaining = price - balance;
    if (remaining <= 0) return "0";
    return remaining.toLocaleString();
  }



  function formatAmountForDisplay(amount: unknown): string {
    if (typeof amount === "number" && !isNaN(amount)) {
      return amount.toLocaleString();
    }

    if (typeof amount === "string") {
      const numericValue = Number(amount.replace(/,/g, ''));
      if (!isNaN(numericValue)) {
        return numericValue.toLocaleString();
      }
    }

    return "-";
  }


  return (
    <>
      {/* Sticky Bar - Mobile Only */}
      <div className={`fixed bottom-[3.5rem] left-0 right-0 z-[10] md:hidden bg-white border-t  px-2 py-3.5 flex flex-col gap-5 items-center justify-between overflow-auto`}>
        <div className="w-full px-1 flex items-center justify-between gap-3 text-sm font-medium">
          {
            paymentMethod === 'wallet' ? (
                <div className="flex justify-between !font-black w-full text-base px-2">
                  <span>قابل پرداخت</span>
                  <span>{getPayableAfterWallet(finalPrice, userData?.balance)} تومان</span>
                </div>
              
            )
              :
              
              <div className="flex justify-between !font-black w-full text-base px-2">
                <span>قابل پرداخت</span>
                <span>{finalPrice ? finalPrice.toLocaleString() : "-"} تومان</span>
              </div>
          }
        </div>
        {steps.title === "payment" ? (
          <div className="w-[95%] mx-auto">
            <CustomButton
              onClick={() => {
                setIsNavigating(true)
                onCreateInvoice?.()
              }}
              className="py-5 px-4 max-md:py-3.5 max-md:px-1 max-md:text-sm"
              disabled={isNavigating || cartItems.length === 0}
            >
              {isNavigating ? (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              ) : (
                showCompleteProfile ? "تکمیل پروفایل و پرداخت" : paymentMethod === 'wallet' ? "پرداخت از کیف پول " : "پرداخت آنلاین"
              )}
            </CustomButton>
          </div>
        ) : (
          <CustomButton
            onClick={handleNextStep}
            className="text-sm w-full px-4 py-3 rounded-xl"
            disabled={isNavigating || disabled}
          >
            {isNavigating ? (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            ) : (
              steps.nextStepBtnTitle
            )}
          </CustomButton>
        )}
      </div>

      {/* Full Cart - Desktop Only */}
      <div className={`max-md:w-full md:w-[25%] bg-white rounded-3xl p-5 ${paymentMethod === "wallet" ? "h-[36rem]" : "h-[30rem]"} max-md:h-[24rem] cart-circles half-circle`}>
        <div className="relative title-bt-border w-fit pb-5 mt-5">
          <h2 className='max-md:text-base'>مجموع کل سبد خرید</h2>
        </div>

        <div className="flex flex-wrap justify-between mt-7  pb-4 max-md:text-sm">
          <div className='flex justify-between w-full mb-5 border-dashed border-b-2 pb-4'>
            <span>قیمت کالاها</span>
            <span>{totalPrice ? totalPrice.toLocaleString() : "-"} تومان</span>

          </div>
          <div className='flex justify-between w-full border-dashed border-b-2 pb-4'>
            <span>هزینه ارسال</span>
            <span>{deliveryPrice ? deliveryPrice.toLocaleString() + " تومان" : "-"} </span>

          </div>
        </div>

        <div className="flex justify-between mt-2.5 px-1 border-dashed border-b-2 pb-6 text-red-400 max-md:text-sm">
          <span>تخفیف</span>
          <span>{totalDiscount ? totalDiscount.toLocaleString() : "-"} تومان</span>
        </div>

        <div className="mt-7 max-md:text-sm">
          {
            paymentMethod === 'wallet' ? (
              <>
                <div className="flex justify-between mb-5  pb-4">
                  <span>موجودی کیف پول</span>
                  <span>{formatAmountForDisplay(userData?.balance)} تومان</span>
                </div>

                <div className="flex justify-between max-md:hidden !font-black">
                  <span className='!font-black'>قابل پرداخت</span>
                  <span>{getPayableAfterWallet(finalPrice, userData?.balance)} تومان</span>
                </div>
                
              </>
            )
              :
              
              <div className="flex justify-between !font-black">
                <span>قابل پرداخت</span>
                <span>{finalPrice ? finalPrice.toLocaleString() : "-"} تومان</span>
              </div>
          }





          {/* <div className="flex justify-between mb-5">
            <span>قابل پرداخت</span>
            <span>{finalPrice ? finalPrice.toLocaleString() : "-"} تومان</span>
          </div> */}



          {steps.title === "payment" ? (
            <div className="w-[95%] mx-auto mt-7 max-md:hidden">
              <CustomButton
                onClick={() => {
                  setIsNavigating(true)
                  onCreateInvoice?.()
                }}
                className="py-5 px-4 max-md:py-3.5 max-md:px-1 max-md:text-sm"
                disabled={isNavigating || cartItems.length === 0}
              >
                {isNavigating ? (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  // paymentMethod === 'wallet' ? showCompleteProfile ? "تکمیل پروفایل و پرداخت" : "پرداخت از کیف پول" : "پرداخت آنلاین"
                  showCompleteProfile ? "تکمیل پروفایل و پرداخت" : paymentMethod === 'wallet' ? "پرداخت از کیف پول " : "پرداخت آنلاین"
                )}
              </CustomButton>
              {/* <p className="text-sm mt-5 text-justify text-gray-500 max-md:text-xs max-md:leading-5">
                {paymentMethod === 'wallet'
                  ? userData?.balance && userData.balance > 0
                    ? `موجودی کیف پول شما ${userData.balance.toLocaleString()} تومان است.`
                    : 'موجودی کیف پول شما کافی نیست. لطفا از پرداخت آنلاین استفاده کنید.'
                  : 'پرداخت از طریق درگاه بانکی انجام خواهد شد.'
                }
              </p> */}
              { paymentMethod == "wallet" && formatAmountForDisplay(userData?.balance) < formatAmountForDisplay(finalPrice) ? (
                  <p className="text-sm mt-5 text-justify leading-6 text-gray-500 max-md:text-xs max-md:leading-5">
                    موجودی کیف پول شما کافی نیست. باقی مانده مبلغ قابل پرداخت از طریق درگاه آنلاین پرداخت خواهد شد.
                  </p>
                ) : (
                  ""
                )}
            </div>
          ) : (
            <div className="w-[95%] mx-auto mt-7 max-md:hidden">
              <CustomButton
                onClick={handleNextStep}
                className="py-5 px-4 max-md:py-3.5 max-md:px-1 max-md:text-sm"
                disabled={isNavigating || disabled}
              >
                {isNavigating ? (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  steps.nextStepBtnTitle
                )}
              </CustomButton>
            </div>
          )}
        </div>
      </div>
    </>
  )
}

export default FactorCard
