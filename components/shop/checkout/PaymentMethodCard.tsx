'use client'
import { useState, useEffect } from 'react';
import MoneyBagIcon from "@/components/common/svg/MoneyBagIcon";
import PaymentItem from "./PaymentItem"
import CheckoutProgress from './CheckoutProgress';
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';

type PaymentMethodCardProps = {
    onPaymentMethodChange?: (method: string) => void;
};

const PaymentMethodCard = ({ onPaymentMethodChange }: PaymentMethodCardProps) => {
    const [selected, setSelected] = useState<string>("online");
    // const { userData } = useAuth();
    // console.log(orderSummaryData);


    // // If user has no wallet balance, default to online payment
    // useEffect(() => {
    //     if (userData && userData.balance === 0) {
    //         setSelected("credit");
    //     }
    // }, [userData]);

    // Notify parent component when payment method changes
    useEffect(() => {
        if (onPaymentMethodChange) {
            onPaymentMethodChange(selected);
        }
    }, [selected, onPaymentMethodChange]);

    // Determine if wallet should be shown based on user balance
    // const showWallet = userData?.balance && userData.balance > 0;

    return (
        <>
            <div className='max-md:w-full bg-white rounded-3xl md:p-8 max-md:p-5 md:min-h-[30rem] max-md:min-h-80'>
                <CheckoutProgress
                    steps={[
                        { title: 'سبد خرید', status: 'completed' },
                        { title: 'انتخاب آدرس', status: 'completed' },
                        { title: 'پرداخت', status: 'current' },
                    ]}
                />
                <div className="header flex justify-between">
                    <div className='flex flex-col gap-3 relative pb-5 title-bt-border'>
                        <h2 className='text-xl font-black max-md:text-base max-md:flex max-md:items-center gap-2'>
                            <Link href={'/checkout/shipping'}>
                                <ArrowRight size={24} className='md:hidden' />
                            </Link>    روش پرداخت
                        </h2>
                        <span className='text-gray-400 font-thin max-md:text-sm'>روش پرداخت مورد نظرتان را انتخاب کنید</span>
                    </div>
                    <div>
                        <MoneyBagIcon className='max-md:w-8' width={70} height={70} />
                    </div>
                </div>
                <p className='text-sm mt-5 text-gray-400 leading-7 max-md:text-xs max-md:leading-5'>
                    بانک مورد نظرتان را انتخاب و یا پرداخت را از کیف پول خود پرداخت کنید
                </p>

                {/* Show wallet payment option if user has balance */}
                <PaymentItem id="wallet" selected={selected} onSelect={setSelected} />


                {/* Always show online payment option */}
                <PaymentItem id="online" selected={selected} onSelect={setSelected} />
            </div>
        </>
    );
};

export default PaymentMethodCard;
