import Image from "next/image"
import PaymentSuccess from "@/public/assets/images/payment-success.png"
import { ProductTransactionResponse } from "@/lib/types/transaction.types"
import Container from "@/components/common/Container"
import PaymentStatusHeader from "@/components/PaymentPage/PaymentStatusHeader"
import PaymentDetails from "@/components/PaymentPage/PaymentDetails"
import PaymentDetailRow from "@/components/PaymentPage/PaymentDetailRow"
import Link from "next/link"
const OrderFactorBox = ({ statusResponse }: { statusResponse: ProductTransactionResponse }) => {
    const statusData = statusResponse?.data
    return (

        <section className='md:w-[680px] max-md:!w-[96%] max-md:mx-auto '>
            <div className='max-w-md rounded-3xl bg-white p-3 md:my-32 my-3 pb-5 mx-auto shadow-md max-md:mb-16 max-md:mt-8 px-4'>

                <PaymentStatusHeader
                    isSuccess={statusResponse.success}
                />


                {statusData  && (
                    <div className="payment-card-body flex flex-col gap-3 mt-5">
                        <PaymentDetailRow label="شناسه سفارش" value={statusData.payable_id} />
                        <PaymentDetailRow label="کد سفارش" value={statusData.code} />
                        <PaymentDetailRow label="تاریخ ثبت" value={statusData.created_at} />
                        <PaymentDetailRow label="مبلغ پرداختی" value={statusData.amount} />

                    </div>
                )}


                <div className='w-full text-center mx-auto mt-10'>
                    <Link href={`/order/status?payable_id=${statusData?.payable_id}`} className="bg-primary text-white py-3 text-base rounded-2xl px-4">
                        مشاهده فاکتور
                    </Link>
                </div>
            </div>
        </section>


    )
}

export default OrderFactorBox