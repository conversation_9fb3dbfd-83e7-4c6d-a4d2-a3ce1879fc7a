'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { ProductTransactionResponse } from '@/lib/types/transaction.types'
// import LoadingSpinner from '@/components/UI/LoadingSpinner'

interface PaymentRedirectProps {
  statusResponse: ProductTransactionResponse
}

/**
 * PaymentRedirect component that shows loading state and redirects to order status page
 * @param statusResponse - The payment transaction response data
 */
const PaymentRedirect = ({ statusResponse }: PaymentRedirectProps) => {
  const router = useRouter()
  const [countdown, setCountdown] = useState(3)
  const statusData = statusResponse?.data

  useEffect(() => {
    if (statusResponse && statusData?.payable_id) {
      console.log('🔄 Payment response received, preparing redirect...', {
        success: statusResponse.success,
        payable_id: statusData.payable_id
      })

      // Countdown timer
      const countdownInterval = setInterval(() => {
        setCountdown(prev => {
          const newCount = prev - 1
          if (newCount <= 0) {
            clearInterval(countdownInterval)
          }
          return newCount
        })
      }, 1000)

      return () => clearInterval(countdownInterval)
    } else {
      console.error('❌ Invalid payment response or missing payable_id:', statusResponse)
    }
  }, [statusResponse, statusData])

  // Separate useEffect for redirect when countdown reaches 0
  useEffect(() => {
    if (countdown <= 0 && statusData?.payable_id) {
      console.log(' Redirecting to order status page...')
      router.push(`/order/status?payable_id=${statusData.payable_id}`)
    }
  }, [countdown, statusData?.payable_id, router])

  // Handle error cases
  if (!statusResponse) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[50vh] py-10">
        <div className="text-red-500 text-center">
          <p className="text-lg font-semibold">خطا در دریافت اطلاعات پرداخت</p>
          <p className="mt-2 text-sm">لطفاً دوباره تلاش کنید</p>
        </div>
      </div>
    )
  }

  if (!statusData?.payable_id) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[50vh] py-10">
        <div className="text-yellow-600 text-center">
          <p className="text-lg font-semibold">اطلاعات سفارش ناقص است</p>
          <p className="mt-2 text-sm">در حال بررسی...</p>
        </div>
      </div>
    )
  }

  return (
    // <div className="flex flex-col items-center justify-center min-h-[50vh] py-10 px-4">
    //   <div className="bg-white rounded-2xl shadow-lg p-8 max-w-md w-full text-center">
    //     <LoadingSpinner size="large" className="mx-auto" />

    //     <h2 className="mt-6 text-xl font-semibold text-gray-800">
    //       {statusResponse.success ? 'پرداخت موفق!' : 'در حال بررسی پرداخت...'}
    //     </h2>

    //     <p className="mt-4 text-gray-600">
    //       در حال انتقال به صفحه وضعیت سفارش...
    //     </p>

    //     <div className="mt-4 p-3 bg-gray-50 rounded-lg">
    //       <p className="text-sm text-gray-700">
    //         <span className="font-medium">شماره سفارش:</span> {statusData.payable_id}
    //       </p>
    //       {statusData.code && (
    //         <p className="text-sm text-gray-700 mt-1">
    //           <span className="font-medium">کد سفارش:</span> {statusData.code}
    //         </p>
    //       )}
    //     </div>

    //     <p className="mt-4 text-sm text-gray-500">
    //       انتقال خودکار در {countdown} ثانیه...
    //     </p>
    //   </div>
    // </div>
    <div className="flex flex-col gap-5 justify-center items-center h-[90vh]">
      <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin" />
    </div>
  )
}

export default PaymentRedirect
