"use client"
import Map from "@/public/assets/images/map.png"
import UserAddressItem from "../checkout/UserAddressItem"
import { InvoiceAddress } from "@/lib/types/invoice.types"

const AddressWrapper = ({address}: {address: InvoiceAddress}) => {
  return (
    // TODO: an id must be added to invoice address from backend
    <UserAddressItem editable={false} {...address} selectable={false} id="1" selected={"1"} onSelect={() => {}} image={Map} />
  )
}

export default AddressWrapper