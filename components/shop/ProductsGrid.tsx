import Product from "@/components/shop/mainPage/Product";
import ProductSkeletonDesktop from "@/components/shop/skeleton/ProductSkeletonDesktop";
import ProductSkeletonMobile from "@/components/shop/skeleton/ProductSkeletonMobile";
import React, {RefObject} from "react";
import {SimilarProduct} from "@/lib/types/product.types";
import {undefined} from "zod";

type Props = {
    items: SimilarProduct[];
    isMobile: boolean;
    mobileUsePagination: boolean;
    hasMore: boolean;
    productLoadingPagination: boolean;
    productLoadingInfinity: boolean;
    observeRef: RefObject<HTMLDivElement>;
    itemsCount: number;
    mobilePaginationThreshold: number;
    isFetchingNextPage: boolean;
};

export default function ProductsGrid({
                                         items,
                                         isMobile,
                                         mobileUsePagination,
                                         hasMore,
                                         productLoadingPagination,
                                         productLoadingInfinity,
                                         observeRef,
                                         itemsCount,
                                         mobilePaginationThreshold,
                                         isFetchingNextPage
                                     }: Props) {
    const shouldUseObserver = (index: number) =>
        (isMobile &&
            !mobileUsePagination &&
            itemsCount < mobilePaginationThreshold &&
            hasMore &&
            index === items.length - 2);

    return (
        <div className='min-h-[70vh]'>
            <div
                className='w-full grid grid-cols-1 sm:grid-cols-2 mb-10 md:grid-cols-2 lg:grid-cols-3 gap-4'>
                {
                    ((!isMobile || mobileUsePagination) && !productLoadingPagination
                    ) && itemsCount > 0 &&
                    (items.map((product, index) => (
                        <Product
                            ref={(shouldUseObserver(index)) ? observeRef : undefined}
                            key={index}
                            product={product}
                        />
                    )))
                }
                {
                    (isMobile && !mobileUsePagination && (!productLoadingInfinity || isFetchingNextPage)
                    ) && itemsCount > 0 &&
                    (items.map((product, index) => (
                        <Product
                            ref={((isMobile && !mobileUsePagination && itemsCount < mobilePaginationThreshold) && index === items.length - 2) ? observeRef : undefined}
                            key={index}
                            product={product}
                        />
                    )))
                }
                {
                    (!isMobile && productLoadingPagination) && (
                        <>
                            {
                                Array.from({length: 6}).map((_, index) => <ProductSkeletonDesktop key={index}/>)
                            }
                        </>
                    )
                }
                {((isMobile && productLoadingInfinity ||
                        (hasMore && isFetchingNextPage)) ||
                    (isMobile && mobileUsePagination && productLoadingPagination)) && (
                    <>
                        {
                            Array.from({length: 3}).map((_, index) => <ProductSkeletonMobile key={index}/>)
                        }
                    </>
                )
                }
                {
                    items &&
                    itemsCount === 0 && (
                        <p className="text-center col-span-full mt-8">هیچ محصولی یافت نشد.</p>
                    )
                }
            </div>

        </div>
    );
}