'use client'

import {SearchIcon, XIcon} from 'lucide-react'
import React, {ChangeEvent, Dispatch, SetStateAction, useEffect, useMemo, useState} from 'react'
import AccordionHeader from './AccordionHeader'
import {ProductFilterOptions} from "@/lib/types/product.types";
import debounce from 'lodash/debounce';
import {useRouter} from "next/navigation";

type Props = {
    value: string,
    open?: boolean,
    setProductParams?: Dispatch<SetStateAction<ProductFilterOptions>>
    onClose?: () => void
}

const SearchMobileInput = ({setProductParams, value, open, onClose}: Props) => {
    const [inputValue, setInputValue] = useState(value);
    const router = useRouter();

    useEffect(() => {
        setInputValue(value);
    }, [value]);

    const debouncedSearch = useMemo(() => {
        return debounce((value: string) => {
            setProductParams?.((prevState) => ({
                ...prevState,
                search: value,
                page: 1
            }));
        }, 700);
    }, [setProductParams]);

    const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value;
        setInputValue(value);
        debouncedSearch(value);
    };

    const handleLinkClick = () => {
        router.push(`/category?search=${encodeURIComponent(inputValue.trim())}`);
        onClose?.();
    }

    const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
        if (event.key === 'Enter') {
            router.push(`/category?search=${encodeURIComponent(inputValue.trim())}`);
            onClose?.(); // Optional: close the filter if needed
        }
    };

    const handleClear = () => {
        setInputValue('');
        setProductParams?.((prevState) => ({
            ...prevState,
            search: '',
            page: 1
        }));
    };

    return (
        <div className=' bg-white min-h-20 rounded-3xl p-3 search-products-filter lg:hidden'>
            <AccordionHeader
                title='جستجو'
                open={open}
                showToggleBtn={false}
                showCloseBtn={true}
                onCloseBtnClick={() => onClose?.()}
            >
                <div className='mt-5'>
                    <div className="md:hidden relative ">
                        <input
                            value={inputValue || ''}
                            type="text"
                            placeholder="دنبال چه چیزی میگردی؟"
                            className="w-full py-3 pl-4 pr-10 text-gray-500 bg-gray-100
                             rounded-full outline-none focus:ring-0 focus:ring-gray-300 placeholder-gray-400 text-sm text-right"
                            onKeyDown={handleKeyDown}
                            onChange={handleChange}
                        />
                        <SearchIcon
                            onClick={handleLinkClick}
                            className="absolute size-5 right-3 top-1/2 -translate-y-1/2 text-gray-400 text-lg cursor-pointer"/>
                        {inputValue && (
                            <button
                                onClick={handleClear}
                                className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"
                            >
                                <XIcon className="size-4"/>
                            </button>
                        )}
                    </div>

                </div>
            </AccordionHeader>
        </div>
    )
}

export default SearchMobileInput