import {ChevronLeft} from 'lucide-react'
import AccordionHeader from './AccordionHeader'
import {Category} from "@/lib/types/category.types";
import Link from "next/link";
import {useState} from "react";

type Props = {
    categories: Category[]
    open?: boolean
    onClose?: () => void
    showCloseBtn?: boolean
    showToggleBtn?: boolean
}

const CategoryItem = ({category, onClose}: { category: Category; onClose?: () => void }) => {
    const hasChildren = category.children && category.children.length > 0;
    const [isOpen, setIsOpen] = useState(false);

    const toggle = () => setIsOpen((prev) => !prev);

    return (
        <li className="flex flex-col gap-2">
            <div className="flex items-center justify-between relative">
                <Link onClick={() => onClose?.()} href={`/category/${category.slug}`}>
                    {category.title}
                </Link>

                {hasChildren && (
                    <button onClick={toggle} className="flex justify-end relative">
                        <ChevronLeft
                            className={`text-gray-400 transition-transform duration-200 ${isOpen ? '-rotate-90' : ''}`}
                        />
                        <ChevronLeft
                            className={`text-gray-300 absolute -left-2 top-0 transition-transform duration-200 ${isOpen ? '-rotate-90' : ''}`}
                        />
                    </button>
                )}
            </div>

            {hasChildren && isOpen && (
                <ul className="flex flex-col gap-y-5 border-r border-dashed pr-5 mt-1">
                    {(category?.children || []).map((child) => (
                        <CategoryItem key={child.slug} category={child} onClose={onClose}/>
                    ))}
                </ul>
            )}
        </li>
    );
};

const CategoryFilter = ({
                            categories = [],
                            open,
                            onClose,
                            showToggleBtn,
                            showCloseBtn,
                        }: Props) => {
    return (
        <div className="bg-white min-h-20 rounded-3xl p-3 search-products-filter">
            <AccordionHeader
                showCloseBtn={showCloseBtn}
                showToggleBtn={showToggleBtn}
                onCloseBtnClick={onClose}
                open={open}
                title="دسته بندی"
            >
                <ul className="flex flex-col gap-5 mb-3 text-sm h-fit">
                    {categories.map((category) => (
                        <CategoryItem key={category.slug} category={category} onClose={onClose}/>
                    ))}
                </ul>
            </AccordionHeader>
        </div>
    );
};

export default CategoryFilter;