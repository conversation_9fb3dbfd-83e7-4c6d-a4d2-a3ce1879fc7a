import React, {Dispatch, SetStateAction} from 'react'
import {ProductFilterOptions} from "@/lib/types/product.types";

type Props = {
    value?: string
    setProductParams?: Dispatch<SetStateAction<ProductFilterOptions>>
}


const AvailableProductsFilter = ({setProductParams, value}: Props) => {

    function handleChange(e: React.ChangeEvent<HTMLInputElement>) {
        let value: 'false' | 'true' = 'false'
        if (e.target.checked) {
            value = 'true'
        }
        setProductParams?.((prevState) => ({...prevState, in_stock_only: value, page: 1}))
    }

    return (
        <div
            className='bg-white max-md:border max-md:mb-3 border-gray-200 min-h-20 rounded-3xl p-3 search-products-filter flex justify-between'>
            <label className="flex w-full items-center cursor-pointer justify-between">
                <span className="ms-3 text-sm font-medium  text-gray-900 dark:text-gray-300"> فقط محصولات موجود </span>
                <input type="checkbox"
                       value={value}
                       checked={value === 'true'}
                       onChange={handleChange}
                       className="sr-only peer"/>
                <div
                    className="relative w-11 h-6 bg-gray-200  rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600 "></div>

            </label>
        </div>
    )
}

export default AvailableProductsFilter