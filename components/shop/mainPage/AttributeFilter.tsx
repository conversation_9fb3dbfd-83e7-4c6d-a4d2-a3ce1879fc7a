import React, {useCallback, useMemo} from 'react'
import AccordionHeader from './AccordionHeader'
import {CategorySearchableAttributes} from "@/lib/types/category.types"

type Props = {
    attribute: CategorySearchableAttributes;
    selectedValue?: string; // comma-separated values
    onChange?: (key: string, value: string) => void; // key = attribute_english_title
}

const AttributeFilter = ({attribute, selectedValue, onChange}: Props) => {
    const selectedArray = useMemo(() => {
        return selectedValue?.split(',').filter(Boolean) || [];
    }, [selectedValue]);

    const handleToggle = useCallback((value: string) => {
        const updated = selectedArray.includes(value)
            ? selectedArray.filter(v => v !== value)
            : [...selectedArray, value];

        onChange?.(`attribute_${attribute.english_title}`, updated.join(','));
    }, [selectedArray, onChange, attribute.english_title]);

    if (!attribute || attribute.values.length === 0) return null;

    return (
        <div
            className='bg-white max-md:border max-md:mb-3 border-gray-200 min-h-20 rounded-3xl p-3 search-products-filter'>
            <AccordionHeader
                open={false}
                title={attribute.title}>
                <div className='mt-5'>
                    <ul className='flex flex-col gap-5 text-sm font-light'>
                        {attribute.values.map((val, index) => (
                            <li key={index} className="flex justify-between items-center mb-2">
                                <div className='flex items-center'>
                                    <div className="custom-checkbox px-0">
                                        <label className="flex items-center gap-2 text-sm font-medium">
                                            <input
                                                type="checkbox"
                                                checked={selectedArray.includes(val.title)}
                                                onChange={() => handleToggle(val.title)}
                                            />
                                            {val.title}
                                        </label>
                                    </div>
                                </div>
                            </li>
                        ))}
                    </ul>
                </div>
            </AccordionHeader>
        </div>
    );
};

export default AttributeFilter;