import AccordionHeader from './AccordionHeader';
import {CategorySearchableAttributes} from "@/lib/types/category.types";
import {cn} from "@/lib/utils";
import {useMemo} from "react";

type Props = {
    colors?: CategorySearchableAttributes
    color?: string
    onChange?: (value: string) => void
}


function lightenColor(hex: string, percent: number): string {
    const num = parseInt(hex.replace("#", ""), 16);
    const amt = Math.round(2.55 * percent);

    const R = Math.min(255, (num >> 16) + amt);
    const G = Math.min(255, ((num >> 8) & 0x00FF) + amt);
    const B = Math.min(255, (num & 0x0000FF) + amt);

    return `rgb(${R}, ${G}, ${B})`;
}

const SelectColor = ({colors, color, onChange}: Props) => {

    const selectedColors = useMemo(() => {
        return color?.split(',').map(c => c.trim()) || [];
    }, [color]);

    const handleOnChange = (title: string) => {
        const isSelected = selectedColors.includes(title);
        let updatedColors: string[] = [];

        if (isSelected) {
            updatedColors = selectedColors.filter(c => c !== title);
        } else {
            updatedColors = [...selectedColors, title];
        }
        onChange?.(updatedColors.join(','));
    }

    if (!colors || (colors && colors.values.length === 0)) return null;

    return (
        <div
            className="bg-white max-md:border max-md:mb-3 border-gray-200 min-h-20 rounded-3xl p-3 search-products-filter">
            <AccordionHeader title="انتخاب رنگ">
                <div className="">
                    <div
                        className="w-full grid grid-cols-3 max-md:grid-cols-4 gap-5 px-2">
                        {colors?.values.map((c, index) => {

                            const isActive = selectedColors.includes(c.title);

                            return (
                                <div key={index}
                                     className="flex max-md:pb-5 flex-col gap-2 items-center justify-center cursor-pointer"
                                     onClick={() => handleOnChange(c.title)}
                                >
                                    <div
                                        className={cn('rounded-full border-[2px] border-dashed border-gray-200 p-[2px]', {
                                            "border-gray-500": isActive
                                        })}
                                    >
                                        <div
                                            className={cn(`max-md:w-[40px] max-md:h-[40px] md:w-[50px] md:h-[50px] rounded-full`)}
                                            style={{
                                                background: `linear-gradient(to left,
                                                 ${lightenColor(c.value, 80)} 0%,
                                                 ${lightenColor(c.value, 80)} 25%,
                                                 ${lightenColor(c.value, 60)} 25%,
                                                 ${lightenColor(c.value, 60)} 50%,
                                                 ${lightenColor(c.value, 30)} 50%,
                                                 ${lightenColor(c.value, 30)} 75%,
                                                 ${lightenColor(c.value, 0)} 75%,
                                                 ${lightenColor(c.value, 0)} 100%)`
                                            }}
                                        ></div>
                                    </div>
                                    <span className='text-sm'>{c.title}</span>
                                </div>
                            );
                        })}
                    </div>
                </div>
            </AccordionHeader>
        </div>
    );
};

export default SelectColor;