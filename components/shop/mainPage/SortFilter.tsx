import AccordionHeader from "@/components/shop/mainPage/AccordionHeader";
import React from "react";
import {SortType} from "@/lib/types/product.types";
import {Check} from "lucide-react";

type Props = {
    sortingOptions: { label: string; value: SortType }[]
    onSortSelect: (value: SortType) => void;
    onClose: () => void;
    selectedValue?: SortType;
}

const SortFilter = ({sortingOptions, onSortSelect, onClose, selectedValue}: Props) => {
    return (
        <div className='bg-white min-h-20 rounded-3xl p-3 search-products-filter'>
            <AccordionHeader
                title='مرتب سازی بر اساس:'
                showCloseBtn={true}
                showToggleBtn={false}
                open={true}
                onCloseBtnClick={() => onClose?.()}
            >
                <ul
                    className="w-full bg-white shadow-md rounded-xl h-fit"
                    onClick={(e) => e.stopPropagation()} // Prevents closing when clicking inside
                >
                    {sortingOptions.map(({value, label}, index) => (
                        <li
                            key={index}
                            className="px-2 flex justify-between items-center hover:bg-gray-200 border-b border-gray-200 py-3 cursor-pointer z-20"
                            onClick={() => {
                                onSortSelect?.(value);
                                onClose?.();
                            }}
                        >
                            <span className='text-sm'>{label}</span>
                            {value === selectedValue && <Check/>}
                        </li>
                    ))}
                </ul>
            </AccordionHeader>


        </div>
    )
}

export default SortFilter;