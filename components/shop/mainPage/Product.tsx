import { SimilarProduct } from "@/lib/types/product.types"
import ProductImage from "@/public/assets/images/product1.png"
import Subtract from "@/public/assets/images/subtract-border-gray.svg"
import { ShoppingBasket, Star } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { formatWithComma } from "@/utils/helpers";
import { isNaN } from "lodash";

type Props = {
    ref?: any
    width?: string
    product: SimilarProduct
}

const Product = ({ width, product, ref }: Props) => {

    const { title, slug, rate, image, price, sale_price, actualy_price, in_stock } = product
    console.log(product);
    
    return (
        <Link ref={ref} href={`/product/${slug}?product_id=${product.product_id}`} className='w-full'>
            <div
                className={`bg-white ${width || "product-w-32"} !w-full rounded-3xl p-3 relative max-md:flex max-md:min-h-[140px] md:min-h-[290px] max-md:max-h-40 max-md:gap-3 max-md:items-center`}>
                {/* <Image src='/assets/images/product.svg' alt='product svg' height={120} width={120}
                       className='absolute bottom-[-5px] left-0'/> */}
                <div className="relative max-md:w-[35%] md:full h-full">
                    <div
                        className='relative flex justify-center md:mb-1 rounded-3xl w-full h-[160px] max-md:h-[110px] overflow-hidden'>
                        <Image alt={image?.caption || title} src={image?.url || ProductImage}
                            fill className='h-full w-full object-cover' />
                    </div>
                    {(!isNaN(Number(rate))) && <span
                        className="max-md:hidden bottom-[-10px] left-5 max-md:text-xs absolute flex items-start gap-1
                         bg-[#000] text-white md:py-1 w-[60px] rounded-3xl justify-center text-xs"> {+rate?.toFixed(1)}
                        <Star className="md:w-4 md:h-4 text-[#F7BC06]" fill="#F7BC06" /> </span>}
                </div>
                <div className='max-md:w-[65%] card-details flex flex-col md:gap-1 max-md:gap-1'>
                    <h3 className='mb-2 py-3 text-sm text-gray-800 font-light overflow-hidden'> {title} </h3>
                    {
                        !in_stock && (
                            <span className='max-md:text-sm text-red-500'>عدم موجودی</span>
                        )
                    }
                    <span
                        className=" text-sm max-md:text-xs flex items-center gap-1 bg-[#F9FAFB]  w-fit py-1.5 px-2 rounded-3xl justify-center md:hidden max-md:flex max-md:mr-auto max-md:justify-end max-md:px-2"> 4.9
                        <Star
                            className="w-5 h-4 text-[#F7BC06]" fill="#F7BC06" /> </span>
                    <div className="absolute -bottom-[10px] -left-[10px]">
                        <Image src={image?.url || Subtract} fill alt={image?.caption || title} />
                    </div>
                    {
                        (in_stock && !sale_price) &&
                        <span className='max-md:text-sm text-black text-left'> {formatWithComma(price.toString())}
                            <span
                                className='text-xs text-[#878787] mr-1'>
                                تومان</span>
                        </span>
                    }
                    {
                        (in_stock && sale_price) && <div className="flex  flex-col max-md:gap-y-2">
                            <div className="text-red-500 text-left">
                                <span
                                    className='max-md:text-sm font-bold'> {formatWithComma(sale_price.toString())}
                                    <span
                                        className='text-xs mr-1 font-light'>تومان
                                    </span>
                                </span>
                            </div>
                            <div className="relative text-gray-400 text-sm max-md:text-xs text-left">
                                <span className="relative">
                                    <span
                                        className='max:md:text-xs md:text-sm max-md:text-xs font-bold'> {formatWithComma(actualy_price.toString())}
                                        <span
                                            className='text-xs text-[#878787] mr-1'>تومان</span>
                                    </span>
                                    <div className="absolute inset-0 flex items-center justify-center">
                                        <div
                                            className="w-full h-px bg-black transform -rotate-12 origin-center"></div>
                                    </div>
                                </span>
                            </div>
                            
                        </div>
                    }


                    {/* <div>
                        <button
                            className='bg-gray-200 p-[9px] rounded-full border-2 border-white absolute -bottom-0 md:left-9 max-md:left-8 transition-colors duration-300 hover:bg-yellow hover:text-white'>
                            <ShoppingBasket className='w-6'/>
                        </button>
                    </div> */}
                </div>
            </div>
        </Link>
    )
}

export default Product