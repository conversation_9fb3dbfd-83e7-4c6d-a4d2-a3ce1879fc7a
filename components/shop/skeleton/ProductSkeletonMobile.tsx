export default function ProductSkeletonMobile() {
    return (
        <div className="w-full max-h-[140px] max-w-md md:hidden bg-white rounded-2xl shadow-sm p-4" dir="rtl">
            <div className="flex flex-row-reverse items-center">
                {/* Action Button Skeleton (Right Side in RTL) */}
                <div className="w-10 h-10 bg-gray-100 rounded-full animate-pulse flex-shrink-0 ml-4"></div>

                {/* Content Area (Center) */}
                <div className="flex-1 space-y-4">
                    {/* Title Skeleton */}
                    <div className="space-y-2">
                        <div className="h-5 bg-gray-200 rounded animate-pulse w-3/4"></div>

                        {/* Blue Underline Skeleton */}
                        <div className="relative pt-1">
                            <div className="h-0.5 bg-gray-200 rounded w-full"></div>
                            <div className="absolute top-1 right-0 h-0.5 bg-blue-200 rounded w-1/4 animate-pulse"></div>
                        </div>
                    </div>

                    {/* Price Skeleton */}
                    <div className="flex items-center justify-start">
                        <div className="h-2 w-2 bg-gray-300 rounded-full ml-2 animate-pulse"></div>
                        <div className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div>
                    </div>

                    {/*/!* Rating Skeleton *!/*/}
                    {/*<div className="flex items-center justify-start">*/}
                    {/*    <div*/}
                    {/*        className="w-5 h-5 bg-yellow-100 rounded-full ml-1 animate-pulse flex items-center justify-center">*/}
                    {/*        <div className="w-3 h-3 bg-yellow-200 rounded-full"></div>*/}
                    {/*    </div>*/}
                    {/*    <div className="h-4 bg-gray-200 rounded w-8 animate-pulse"></div>*/}
                    {/*</div>*/}
                </div>

                {/* Product Image Skeleton (Left Side in RTL) */}
                <div
                    className="relative w-20 h-20 bg-gray-200 rounded-lg animate-pulse flex-shrink-0 ml-4 overflow-hidden">
                    <div
                        className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-shimmer-rtl"></div>
                </div>
            </div>
        </div>
    )
}
