export default function ProductSkeletonDesktop() {
    return (
        <div
            className="w-full max-md:hidden bg-white rounded-2xl shadow-sm border mb-5 border-gray-100 overflow-hidden">
            {/* Product Image Skeleton */}
            <div className="relative h-48 bg-gray-200 animate-pulse">
                <div
                    className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-shimmer"></div>
            </div>

            {/* Rating Badge Skeleton */}
            <div className="absolute top-4 left-4">
                <div className="flex items-center bg-gray-300 rounded-full px-3 py-1 animate-pulse">
                    <div className="w-4 h-4 bg-gray-400 rounded-full mr-1"></div>
                    <div className="w-6 h-3 bg-gray-400 rounded"></div>
                </div>
            </div>

            {/* Content Area */}
            <div className="p-4 space-y-4">
                {/* Title Skeleton */}
                <div className="space-y-2">
                    <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4 animate-pulse"></div>
                </div>

                {/* Price Section Skeleton */}
                <div className="flex items-center justify-between pt-2">
                    {/* Price Skeleton */}
                    <div className="space-y-1">
                        <div className="h-6 bg-gray-200 rounded w-24 animate-pulse"></div>
                        <div className="h-3 bg-gray-200 rounded w-16 animate-pulse"></div>
                    </div>

                    {/* Action Button Skeleton */}
                    <div className="w-10 h-10 bg-gray-200 rounded-full animate-pulse"></div>
                </div>
            </div>
        </div>
    )
}