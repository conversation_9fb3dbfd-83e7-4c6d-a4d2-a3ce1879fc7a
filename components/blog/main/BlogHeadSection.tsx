import Comma1 from "@/public/assets/images/comma-blog-2.png"
import Mouse from "@/public/assets/images/blog-mouse.webp"
import Stars from "@/public/assets/images/stars-mouse.svg"
import Image from "next/image"
import { ChevronLeft } from "lucide-react"
import SearchBox from "@/components/common/SearchBox"
import { CategoryItem } from "@/lib/types/article.types"
import Link from "next/link"
const BlogHeadSection = ({ categories }: { categories: CategoryItem[] }) => {
    return (
        <section className={`blog-head bg-blog flex flex-col items-center justify-center min-h-[50vh] gap-12 bg-white rounded-b-[15%] ${categories.length ? 'max-md:min-h-[45vh]': 'max-md:min-h-[40vh] max-md:pt-3' }  max-md:rounded-b-[8%]`}>
            <div className="container flex flex-col items-center justify-center gap-12 pb-7 px-3">
                <div className="blog-title flex flex-col gap-3 items-center justify-center">

                    <div className="w-fit mx-auto md:mt-16 flex justify-center gap-3 items-stretch rtl">
                        <Image src={Mouse} alt="" className="h-6 max-md:h-4 w-auto self-end" />
                        <div className="flex items-center gap-2 self-center">
                            <Image src={Comma1} alt="" className="h-3 w-auto" />
                            <h1 className="text-6xl leading-none max-md:text-3xl">وبلاگ</h1>
                            <Image src={Comma1} alt="" className="h-3  w-auto -scale-x-100" />
                        </div>
                        <Image src={Stars} alt="" className="h-10 max-md:h-7 w-auto self-start" />
                    </div>
                    <span className="max-md:text-sm">جدیدترین اخبار و مقالات خودروکس</span>

                </div>
                {/* <div className="search-section relative w-[23rem] max-md:w-[19rem] max-md:text-xs">
                        <Search className="absolute bottom-4 max-md:bottom-[13px] right-2 text-gray-400 max-md:w-4" size={20} />
                        <input type="text" placeholder="دنبال چه چیزی هستین؟" className="w-full py-3.5 px-9 max-md:px-7 rounded-3xl text-sm max-md:text-xs border focus-visible:ring-0" />
                        <button className="bg-primary text-white px-3 py-2 max-md:py-2.5 text-sm max-md:text-xs rounded-3xl absolute bottom-[7px] max-md:bottom-1 left-3 ">
                            جستجو
                        </button>
                    </div> */}
                <SearchBox
                    className="my-4 relative w-[23rem] max-md:w-[19rem]"
                    placeholder="نام محصول را وارد کنید"
                    buttonText="بگرد"
                // onSearch={(value) => console.log("Search:", value)}
                />
                {
                    categories.length ?
                    <div className="btn-groups container w-full flex gap-5 max-md:flex-nowrap max-md:overflow-x-auto max-md:overflow-y-hidden max-md:mx-auto max-md:mt-3 max-md:pb-4 max-md:gap-4 max-md:snap-x max-md:snap-mandatory mb-5">
                        {
                            categories.map((category, index) => (
                                <Link key={index} href={`/blog/${category.slug}`}
                                    style={{ filter: 'drop-shadow(-5px 5px 10px rgba(247,188,6,0.53))' }}
                                    className={`bg-[#F7BC06] border-yellow text-white border px-3 md:pr-5 py-3 rounded-3xl flex items-center justify-between gap-5 filter max-md:!text-xs max-md:!w-[8rem] max-md:!py-3.5 max-md:!h-[50px] max-md:!gap-1 max-md:flex-shrink-0`}>
                                    {category.title}
                                    <ChevronLeft size={16} className="max-md:w-4 max-md:h-4" />
                                </Link>
                            ))
                        }

                        
                        {/* <button
                            className="bg-white border text-gray-800 border-gray-300 px-3 md:pr-5 py-3 rounded-3xl flex items-center justify-between gap-5
    max-md:!text-xs max-md:!w-[8rem] max-md:!py-3.5 max-md:!h-[50px] max-md:!gap-1 max-md:flex-shrink-0">
                            دسته بندی شما
                            <ChevronLeft size={16} className="max-md:w-4 max-md:h-4" />
                        </button> */}
                    

                    </div>
                    :
                    ''
                    
                }

            </div>

        </section>
    )
}

export default BlogHeadSection