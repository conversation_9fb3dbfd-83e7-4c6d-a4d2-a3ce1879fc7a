import CustomButton from "@/components/UI/CustomButton";
import {ChevronsLeft} from "lucide-react";
import {BLOG_PATH} from "@/lib/routes";


export default function BlogSectionHead() {
    return (
        <div className='flex flex-col justify-center items-center gap-2'>
            <h2 className='font-light text-[#242021] text-xl md:text-3xl'>بلاگ خودراکس</h2>
            <CustomButton href={BLOG_PATH} bgColor="bg-white border border-gray-200"
                          className="flex gap-1 w-30 text-[#5E646B] md:mt-2 justify-center rounded-3xl whitespace-nowrap text-xs">
                <span>مشاهده همه</span>
                <ChevronsLeft className="text-[#9DA5B0]"/>
            </CustomButton>
        </div>
    );
}
