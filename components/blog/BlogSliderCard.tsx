import Image from "next/image";
import EmptyCover from "@/public/assets/images/empty-cover.webp";
import Author from "@/public/assets/images/user.webp";
import Link from "next/link";
import StopwatchIcon from "../common/svg/StopwatchIcon";
import { FC } from "react";
import { Article } from "@/lib/types/types";

interface BlogSliderCardProps extends Article {
  isActive?: boolean;
  postInfo?: boolean;
}

const BlogSliderCard: FC<BlogSliderCardProps> = ({
  isActive,
  postInfo,
  title,
  summary,
  slug,
  cover,
  study_time,
  author_fullname,
  author_avatar,
  comments_count,
  date,
}) => {
  console.log(decodeURIComponent(slug));
  
  return (
    <Link
      href={`/blog/${decodeURIComponent(slug)}`}
      className={`relative w-full min-h-[19rem] mb-5 block mt-4 group transition-all duration-300 ${
        isActive
          ? "scale-105 z-10" // Reduced scale to prevent overlap
          : "scale-100 shadow-lg border rounded-3xl"
        } ${
          postInfo
            ? "bg-white !w-full rounded-3xl !min-h-[22rem] max-md:!w-[96%] max-md:max-w-md max-md:!mx-auto"
            : ""
        }`}
    >
      {/* Overlay stays the same */}
      <div className="blog-overlay hidden absolute left-0 top-0 right-0 bottom-0 from-white to-transparent z-10"></div>

      {/* Background changes based on isActive */}
      <div
        className={`rounded-2xl px-4 py-4 transition-all duration-300 ${isActive ? "bg-white shadow-lg" : "bg-transparent"
          }`}
      >
        <div className="relative">
          <div className="w-full aspect-[16/8] relative rounded-2xl overflow-hidden">
            <Image
              src={cover || EmptyCover}
              alt="MotorOil"
              fill
              priority
              className="object-cover"
              sizes="(min-width: 1024px) 33vw, (min-width: 640px) 50vw, 100vw"
            />
          </div>
          <div className="absolute bottom-[-8px] left-[15px] flex items-center bg-[#E7EBEF] justify-between rounded-xl overflow-hidden">
            <span className="bg-gray-400 transition-colors duration-300 group-hover:bg-yellow pr-1 pl-2 rounded-bl-xl text-xs block rounded-r-xl py-1 text-white">
              {date}
            </span>
            <span className="bg-[#E7EBEF] text-xs rounded-l-xl pr-1 pl-2">
              {comments_count} نظر
            </span>
          </div>
        </div>
        <div className="w-full flex mt-4 flex-col gap-y-2">
          <h3 className="text-sm text-gray-600">
            {title}
          </h3>
          {
            postInfo &&
            <div className="flex gap-5 text-xs">
              <div className="flex gap-3 items-center">
                <StopwatchIcon />
                <span>
                  زمان مطالعه: {study_time}
                </span>
              </div>
              <div className="flex gap-3 items-center">
                <Image src={author_avatar || Author} width={30} height={30} alt="author" />
                <span>
                  {author_fullname}
                </span>
              </div>
            </div>
          }
          <div className="relative w-[100px] h-[2px] bg-gray-300">
            <div className="absolute top-0 right-0 w-[30%] h-full bg-gray-500 transition-colors duration-300 group-hover:bg-yellow"></div>
          </div>
          <p
            className="accent-gray-400 leading-relaxed text-xs overflow-hidden text-ellipsis whitespace-normal min-h-[3em] sm:min-h-[4.5em]"
            style={{
              display: "-webkit-box",
              WebkitBoxOrient: "vertical",
              WebkitLineClamp: 2, // مقدار پیش‌فرض موبایل
            }}
            // خط زیر باعث میشه توی سایزهای بزرگ‌تر مقدار clamp تغییر کنه
            data-line-clamp-desktop={3}
          >
            {summary}
          </p>

        </div>
      </div>
    </Link>
  );
};

export default BlogSliderCard;
