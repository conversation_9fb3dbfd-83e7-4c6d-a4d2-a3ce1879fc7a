import Image from "next/image";
import User from "@/public/assets/images/user.webp";


export const AboutAuthor = ({ authorAbout, authorFullName, authorAvatar }: { authorAbout: string, authorFullName: string, authorAvatar: string }) => {
    return (
        <section className="flex max-md:flex-col gap-3 md:items-center p-3 bg-white rounded-3xl max-md:w-[96%] max-md:mx-auto">
            <div className="md:h-full max-md:flex max-md:items-center max-md:gap-3">
                <Image src={authorAvatar || User} alt="user" className=" h-full md:max-w-40 max-md:max-w-16" />
                <h3 className="md:hidden">
                    {authorFullName}
                </h3>
            </div>
            <div className="flex flex-col justify-between md:justify-center h-full gap-4">
                <h3 className="max-md:hidden">
                    {authorFullName}
                </h3>
                <p className="text-justify leading-8 max-md:text-xs max-md:leading-7">
                    {authorAbout}
                </p>
            </div>
        </section>
    )
}
