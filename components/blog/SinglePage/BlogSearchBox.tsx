import SearchBox from "@/components/common/SearchBox";

interface BlogSearchBoxProps {
  className?: string;
}

const BlogSearchBox = ({ className = "" }: BlogSearchBoxProps) => {
  return (
    <div className={className}>
      <h3 className="sidebar-title relative px-5 mt-3">
        جستجو در بلاگ
      </h3>
      {/* TODO: it cant be used in server componetn so i have to create a wraper or something  */}
      <SearchBox
        className="my-4"
        placeholder="نام محصول را وارد کنید"
        buttonText="بگرد"
        // onSearch={(value) => console.log("Search:", value)}
      />
    </div>
  );
};

export default BlogSearchBox;
