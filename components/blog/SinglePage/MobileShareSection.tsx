import FacebookIcon from '@/components/common/svg/FacebookIcon'
import PinterestIcon from '@/components/common/svg/PinterestIcon'
import TelegramIcon from '@/components/common/svg/TelegramIcon'
import TwitterIcon from '@/components/common/TwitterIcon'
import { Mail, Share2 } from 'lucide-react'
const MobileShareSection = () => {
  return (
    <div className="flex md:gap-3 max-md:justify-between max-md:w-full">
        <div className="flex items-center gap-3">
            <Share2 className='max-md:w-5' />
            <span className='max-md:text-sm'>
                اشتراک گذاری:
            </span>
        </div>
        <div className="flex gap-3 items-center">
            <div className=" flex items-center justify-center">
                <TelegramIcon className="w-9 h-9 max-md:w-7 max-md:h-7 bg-[#e3b11bbf] transition-all delay-75 hover:cursor-pointer hover:bg-black p-1.5 rounded-full" />
            </div>
            <div className=" flex items-center justify-center">
                <FacebookIcon className="w-9 h-9 max-md:w-7 max-md:h-7 bg-[#e3b11bbf] transition-all delay-75 hover:cursor-pointer hover:bg-black p-1.5 rounded-full" />
            </div>
            <div className=" flex items-center justify-center">
                <TwitterIcon className="w-9 h-9 max-md:w-7 max-md:h-7 bg-[#e3b11bbf] transition-all delay-75 hover:cursor-pointer hover:bg-black p-1.5 rounded-full" />
            </div>
            <div className=" flex items-center justify-center">
                <PinterestIcon className="w-9 h-9 max-md:w-7 max-md:h-7 bg-[#e3b11bbf] transition-all delay-75 hover:cursor-pointer hover:bg-black p-1.5 rounded-full" />
            </div>
            <div className=" flex items-center justify-center">
                <Mail className="w-9 h-9 max-md:w-7 max-md:h-7 bg-[#e3b11bbf] transition-all delay-75 hover:cursor-pointer hover:bg-black p-1.5 rounded-full " color="white" />
            </div>
        </div>
    </div>
    
  )
}

export default MobileShareSection