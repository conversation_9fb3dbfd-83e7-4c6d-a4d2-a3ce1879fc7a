"use client"
import { MessageCircle } from "lucide-react"
import AccordionWrapper from "@/components/shop/ProductPage/AccordionWrapper" 
import CommentTextArea from "@/components/shop/ProductPage/CommentTextArea" 

import CommentItem from "@/components/shop/ProductPage/CommentItem" 
import { ProductComment } from "@/lib/types/product.types"
import { usePathname, useSearchParams } from "next/navigation"
import { useEffect, useState } from "react"
import { getArticleComments } from "@/actions/comment.action"
import { divIcon } from "leaflet"

const BlogComments = ({ contentId }: { contentId: string }) => {
    const [productComments, setProductComments] = useState<ProductComment[]>([])
    const [loading, setLoading] = useState(false)
    const pathname = usePathname();
    const searchParams = useSearchParams(); // triggers re-render on hash change
    // TODO: later i could move this to a separate empty client component and keep this component ssr
    useEffect(() => {
        setLoading(true)
        const getComments = async () => {
            
            const response = await getArticleComments(contentId);
            if (response.success) {
            setProductComments(response.data);
            }
            console.log(response);
            setLoading(false)
        }
        getComments()
        const hash = window.location.hash;

        if (hash) {
            const element = document.querySelector(hash);
            if (element) {
                // Scroll after a short delay to ensure DOM is loaded
                setTimeout(() => {
                    element.scrollIntoView({ behavior: "smooth" });
                }, 100);
            }
        }
    }, [pathname, searchParams]);
    return (
        <section className="bg-white rounded-3xl p-5 mt-10 scroll-mt-28" id="productComments">
            <AccordionWrapper title="نظرات مشتریان" icon={<MessageCircle />} roundedArrow={false}>
                <div className="mt-5">
                    <CommentTextArea contentId={contentId} commentType="blog" />
                    <div>
                        {loading ? 
                        <div className="text-center flex flex-col gap-3 justify-center items-center h-40">
                            <div className="loader text-center border-t-transparent border-4 border-primary rounded-full w-8 h-8 animate-spin"></div>
                            <p> در حال بارگذاری کامنت ها ... </p>
                        </div>
                        : ""
                        }
                        {
                            productComments.length ?
                            <h4 className="mb-5">
                                {productComments.length} امتیاز و دیدگاه کاربران
                            </h4>
                            : ""

                        }
                        {
                            productComments.length ? productComments.map(item => <CommentItem key={item.id} comment={item} />) :  ""
                        }
                        
                       
                    </div>
                </div>
            </AccordionWrapper>
        </section>
    )
}

export default BlogComments