import { CalendarDays, MessageCircleMore } from 'lucide-react'
import Image from 'next/image'
import Author from "@/public/assets/images/user.webp";
import BlogPic1 from "@/public/assets/images/blog-pic1.png";
import React from 'react';
import { ArticleResponse } from '@/lib/types/article.types';

const BlogHead: React.FC<ArticleResponse['data']> = ({ title, author_fullname, author_avatar, date_ago, comments_count, cover, sidebar_right }) => {
  return (
    <section className="post-head relative md:pt-16 max-md:pt-10 w-full">
     
      {
        !sidebar_right &&
            <div className="flex justify-center items-center gap-8 max-md:gap-5 flex-col h-[50%] mb-10 max-md:p-3.5 max-md:text-justify leading-7">
              <h1 className="md:text-3xl">
                {title}
              </h1>
              <div className="flex gap-10 max-md:gap-5 ">
                <span className="flex items-center gap-3 text-sm max-md:text-xs">
                  <Image src={ author_avatar || Author} alt="author" width={30} height={30} className="max-md:w-5 rounded-full" />
                  {author_fullname}
                </span>
                <span className="flex items-center gap-3 text-sm max-md:text-xs">
                  <CalendarDays className="max-md:w-5 h-4" />
                  {date_ago}
                </span>
                <span className="flex items-center gap-3 text-sm max-md:text-xs">
                  <MessageCircleMore className="max-md:w-5 h-4" />
                  {comments_count} نظر
                </span>
              </div>
            </div>
            

      }
            <div className="relative w-full top-[35%]">
              <div className=" max-md:max-w-[96%] max-md:mx-auto w-full">
                <Image
                  src={cover || BlogPic1}
                  alt=""
                  width={950}
                  height={800}
                  className="object-contain w-full rounded-2xl"
                  priority
                />
              </div>
            </div>
            {
              sidebar_right &&
             <div className="flex mt-8 gap-8 max-md:gap-5 flex-col h-[50%]">
              <h1 className="md:text-3xl">
                {title}
              </h1>
              <div className="flex gap-10 max-md:gap-5 ">
                <span className="flex items-center gap-3 text-sm max-md:text-xs">
                  <Image src={author_avatar || Author} alt="author" className="max-md:w-5" />
                  {author_fullname || ""}
                </span>
                <span className="flex items-center gap-3 text-sm max-md:text-xs">
                  <CalendarDays className="max-md:w-5 h-4" />
                  {date_ago}
                </span>
                <span className="flex items-center gap-3 text-sm max-md:text-xs">
                  <MessageCircleMore className="max-md:w-5 h-4" />
                  {comments_count}
                </span>
              </div>
            </div>
            }
          </section>
  )
}

export default BlogHead