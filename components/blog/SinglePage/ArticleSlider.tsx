'use client';
import BlogPic1 from "@/public/assets/images/blog-pic1.png";
import LearnMore from "@/public/assets/images/learn-more.png";
import { useState } from 'react';
import Image from 'next/image';
import { ChevronRight, ChevronLeft } from 'lucide-react';
import { RandomArticle } from "@/lib/types/article.types";
import Link from "next/link";

// const articles = [
//   {
//     id: 1,
//     title: 'در اینجا عنوان مطلب شما قرار می‌گیرد...',
//     label: 'نوشته قبلی',
//     image: LearnMore,
//   },
//   {
//     id: 2,
//     title: 'در اینجا عنوان مطلب شما قرار می‌گیرد...',
//     label: 'نوشته بعدی',
//     image: BlogPic1,
//   },
//   {
//     id: 3,
//     title: 'در اینجا عنوان مطلب شما قرار می‌گیرد...',
//     label: 'نوشته قبلی',
//     image: LearnMore,
//   },
//   {
//     id: 4,
//     title: 'در اینجا عنوان مطلب شما قرار می‌گیرد...',
//     label: 'نوشته بعدی',
//     image: BlogPic1,
//   },
// ];

export default function ArticleSlider({ articles }: { articles: RandomArticle[] }) {
    const [currentIndex, setCurrentIndex] = useState(0);

    const handleNext = () => {
        if (currentIndex < articles.length - 2) {
            setCurrentIndex(prev => prev + 1);
        }
    };

    const handlePrevious = () => {
        if (currentIndex > 0) {
            setCurrentIndex(prev => prev - 1);
        }
    };

    return (
        <div className="relative w-full py-6 rounded-2xl">
            <div className="flex max-md:flex-col justify-between items-center md:gap-[10%] max-md:gap-3 relative">
                {/* Previous Card */}
                {articles[currentIndex] && (
                    <Link href={`/blog/${articles[currentIndex].slug}`} className="md:w-[40%] flex items-center gap-4 p-6 bg-white rounded-2xl shadow-md transition-all">
                        <div className="flex-1">
                            {/* <p className="text-xs text-gray-500 mb-1">{articles[currentIndex].}</p> */}
                            <p className="text-sm font-medium text-black">{articles[currentIndex].title}</p>
                        </div>
                        <Image
                            src={articles[currentIndex].cover}
                            alt={articles[currentIndex].title}
                            width={100}
                            height={100}
                            className="rounded-xl object-cover"
                        />
                    </Link>
                )}

                {/* Next Card */}
                {articles[currentIndex + 1] && (
                    <Link href={`/blog/${articles[currentIndex + 1].slug}`} className="md:w-[40%] flex items-center gap-4 p-6 bg-white rounded-2xl shadow-md transition-all">
                        <div className="flex-1">
                            {/* <p className="text-xs text-gray-500 mb-1">{articles[currentIndex + 1].label}</p> */}
                            <p className="text-sm font-medium text-black">{articles[currentIndex + 1].title}</p>
                        </div>
                        <Image
                            src={articles[currentIndex + 1].cover}
                            alt={articles[currentIndex + 1].title}
                            width={100}
                            height={100}
                            className="rounded-xl object-cover"
                        />
                    </Link>
                )}

                {/* Navigation Buttons */}
                <div className="absolute max-md:justify-between max-md:w-[80%] left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 flex gap-4">
                    <button
                        onClick={handlePrevious}
                        disabled={currentIndex === 0}
                        className={`p-2 rounded-full shadow-md transition ${
                            currentIndex === 0 
                            ? 'bg-gray-200 text-gray-400 cursor-not-allowed' 
                            : 'bg-blue-500 text-white hover:bg-blue-600'
                        }`}
                    >
                        <ChevronRight size={20} />
                    </button>
                    <button
                        onClick={handleNext}
                        disabled={currentIndex >= articles.length - 2}
                        className={`p-2 rounded-full shadow-md transition ${
                            currentIndex >= articles.length - 2 
                            ? 'bg-gray-200 text-gray-400 cursor-not-allowed' 
                            : 'bg-blue-500 text-white hover:bg-blue-600'
                        }`}
                    >
                        <ChevronLeft size={20} />
                    </button>
                </div>
            </div>
        </div>
    );
}