import { FolderMinus } from 'lucide-react'
import MobileShareSection from './MobileShareSection'
import { CategoryItem } from '@/lib/types/article.types'

const BlogPostCategory = ({ category }: { category: CategoryItem }) => {
    return (
        <section className="weblog-category max-md:hidden bg-yellow font-bold md:container flex items-center justify-between mx-auto text-black px-5 py-3 rounded-3xl">
            <div className="flex items-center gap-3">
                <FolderMinus />
                <span>دسته بندی: {category.title} </span>
            </div>
            <MobileShareSection />
        </section>
    )
}

export default BlogPostCategory