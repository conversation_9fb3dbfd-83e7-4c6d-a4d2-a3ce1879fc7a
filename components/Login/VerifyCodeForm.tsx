import Edit from "@/public/assets/images/edit.svg"
import Image from "next/image"
import {UseFormReturn} from "react-hook-form";
import {LoginFormType} from "@/lib/types/zod-schemas";
import Title from "@/components/common/Title";
import CustomInput from "@/components/UI/CustomInput";
import {FormControl, FormField, FormItem, FormMessage} from "@/components/UI/form";
import {LOGIN_CODE_LENGTH} from "@/lib/constants";

type Props = {
    form: UseFormReturn<LoginFormType>;
    onEditClick?: () => void
}

const VerifyCodeForm = ({form, onEditClick}: Props) => {
    return (
        <div className='login-box-body mt-8'>
            <Title className='pr-2'>کد تایید را وارد کنید</Title>
            <h5 className='text-sm md:text-base mt-2 text-muted-foreground'>کد تائید ارسال شده به شماره موبایل خود را
                وارد کنید</h5>
            <div className="flex items-center gap-x-2 mt-3 text-muted-foreground">
                <span className='text-accent-foreground text-lg font-[300]'>{form.getValues('mobile').slice(4)}</span>
                <span>-</span>
                <span className="text-muted-foreground text-lg">{form.getValues('mobile').slice(0, 4)}</span>
                <span>|</span>
                <span className="flex items-center flex-row-reverse gap-1 cursor-pointer text-sm"
                      onClick={() => onEditClick?.()}> تغییر شماره <Image
                    width={15}
                    src={Edit}
                    alt="edit-svg"/></span>
            </div>
            <FormField
                control={form.control}
                name="code"
                render={({field}) => (
                    <FormItem className='w-full'>
                        <FormControl>
                            <CustomInput
                                variant='secondary'
                                autoFocus
                                align='center'
                                inputMode='numeric'
                                autoComplete='one-time-code'
                                maxLength={LOGIN_CODE_LENGTH}
                                className='mt-6'
                                placeholder={` کد شناسایی ${LOGIN_CODE_LENGTH} رقمی خود را وارد کنید`}
                                {...field}
                            />
                        </FormControl>
                        <FormMessage className='text-sm text-center'/>
                    </FormItem>
                )}
            />
        </div>
    )
}

export default VerifyCodeForm