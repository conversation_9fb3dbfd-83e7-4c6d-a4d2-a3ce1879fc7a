import Checked from "@/public/assets/images/checked.svg"
import Phone from "@/public/assets/images/phone.svg"
import Image from "next/image"
import Title from "@/components/common/Title";
import TextMute from "@/components/common/TextMute";
import CustomInput from "@/components/UI/CustomInput";
import Link from "next/link";
import {FormControl, FormField, FormMessage, FormItem} from "@/components/UI/form";
import {UseFormReturn} from "react-hook-form";
import {LoginFormType} from "@/lib/types/zod-schemas";
import {useEffect, useMemo, useState} from "react";
import {Hand} from "lucide-react";
import {RULES_PATH} from "@/lib/routes";

type Props = {
    form: UseFormReturn<LoginFormType>,
    onMobileInputKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void,
    isInquiry?: boolean,
}

const PhoneNumberForm = ({form, onMobileInputKeyDown, isInquiry}: Props) => {
    const mobileValue = form.watch("mobile");
    const [isValid, setIsValid] = useState(false)

    useEffect(() => {
        const validate = async () => {
            if (mobileValue.length === 11) {
                const result = await form.trigger("mobile");
                setIsValid(result)
            } else {
                if (isValid) {
                    setIsValid(false)
                }
            }
        }
        validate()
    }, [mobileValue, form]);

    const phoneIcon = useMemo(
        () => (
            <Image
                src={Phone}
                alt="Phone icon"
                width={25}
                height={25}
                className="text-neutral-400"
            />
        ),
        []
    );

    const checkedIcon = useMemo(
        () =>
            isValid ? (
                <Image src={Checked} alt="Checked icon" width={20} height={20}/>
            ) : null,
        [isValid]
    );

    return (
        <div className='mt-8'>
            {!isInquiry && (
                <>
                    <Title className='pr-2'>ورود | ثبت نام </Title>
                    <div className="flex items-center gap-x-1 text-lg mt-3 text-foreground">
                        <span>سلام</span><Hand color='orange'/>
                    </div>
                </>
            )}
            <TextMute className='mt-2'>لطفا شماره موبایل خود را وارد کنید</TextMute>
            <div className="mt-4">
                <FormField
                    control={form.control}
                    name="mobile"
                    render={({field}) => (
                        <FormItem className='w-full'>
                            <FormControl>
                                <CustomInput
                                    align='center'
                                    autoFocus
                                    maxLength={11}
                                    inputMode='numeric'
                                    onKeyDown={onMobileInputKeyDown}
                                    direction='ltr'
                                    allowOnlyNumbers
                                    leftIcon={phoneIcon}
                                    rightIcon={checkedIcon}
                                    {...field}
                                />
                            </FormControl>
                            <FormMessage className='text-sm text-center'/>
                        </FormItem>
                    )}
                />

            </div>
            <div className="flex items-center justify-center mb-2 mt-5">
                <p className="ms-2 text-xs md:text-sm font-medium text-muted-foreground">ورود
                    شما به معنای پذیرش <Link href={RULES_PATH} className='text-primary-foreground underline'>شرایط و
                        قوانین</Link> خودروکس
                    است</p>
            </div>
        </div>
    )
}

export default PhoneNumberForm