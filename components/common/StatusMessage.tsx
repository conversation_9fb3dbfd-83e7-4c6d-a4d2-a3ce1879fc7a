import ChoiceWrapper from "@/components/common/ChoiceWrapper";
import {ServiceStatusType} from "@/lib/types/types";

type Props = {
    status: ServiceStatusType
}

export default function StatusMessage({status}: Props) {
    return (
        <ChoiceWrapper
            error
        >
            <div className='w-full flex py-3 gap-y-1'>
                {status === 'DISABLED' && <p className='w-full text-red-500 text-center text-xs md:text-sm'>در حال حاضر
                    این بخش
                    غیر فعال است</p>}
                {status === 'COMING_SOON' &&
                    <p className='w-full text-red-500 text-center text-xs md:text-sm'>این بخش بزودی فعال می شود.</p>}
            </div>
        </ChoiceWrapper>
    );
}
