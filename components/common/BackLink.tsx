'use client'

import { useRouter } from 'nextjs-toploader/app';
import ReturnIcon from "@/components/common/svg/ReturnIcon";

type Props = {
    href?: string,
    onClick?: () => void,
    title?: string
}

export default function BackLink({href, onClick, title = "بازگشت"}: Props) {
    const {back, push, refresh} = useRouter()

    const handleBack = () => {
        back();
        setTimeout(() => {
            refresh()
        }, 50); // Short delay ensures navigation completes before reload
    };

    function onLinkClick() {
        if (onClick) {
            onClick?.()
        } else {
            // eslint-disable-next-line @typescript-eslint/no-unused-expressions
            href ? push(href) : handleBack()
        }

    }

    return (
        <div
            className='absolute flex justify-center items-center top-[25px] left-0 w-[60px]  md:w-[100px] h-[45px] bg-gradient-to-l from-[#F9FAFB] to-white rounded-r-full'>
            <span className='cursor-pointer' onClick={onLinkClick}>
            <ReturnIcon/>
            </span>
        </div>
    );
}
