'use client'

import {SimilarProduct} from "@/lib/types/product.types";
import {ArrowLeft, Search} from "lucide-react";
import Link from "next/link";

type Props = {
    isLoading: boolean;
    products: SimilarProduct[]
    searchValue?: string
}

export default function SearchResults({isLoading, products = [], searchValue}: Props) {
    return (
        <>
            <div className="w-full flex flex-col overflow-y-auto">
                {searchValue && <div className="pt-2">
                    <div className="flex align-center py-2 px-5">
                        <div className="flex pl-4">

                        </div>
                        <p className="text-subtitle-strong text-neutral-700 flex align-center">
                            <span className='text-xs'>جستجوی</span>
                            <span className='mr-1 text-sm md:text-sm'>{`"${searchValue}"`}</span>
                        </p>
                    </div>
                </div>}
                {
                    isLoading && (
                        Array.from({length: 3}).map((_, index) => (
                            <div key={index} className="w-full max-w-md mx-auto p-3">
                                <div className="flex items-center gap-x-4">
                                    {/* Icon skeleton */}
                                    <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse"></div>
                                    {/* Content skeleton */}
                                    <div className="flex-1 space-y-2">
                                        <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4"></div>
                                        <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2"></div>
                                    </div>

                                </div>
                            </div>
                        ))
                    )
                }
                {!isLoading && searchValue && <div className="flex flex-col w-full border-t border-gray-100 py-5">
                    {products.map((result, index) => (
                        <Link
                            onClick={() => {
                                console.log('helooooooooooooooooooooooooooooooooooooooooo')
                            }}
                            key={index}
                            href={`/product/${result.slug}`}
                            className="flex items-center px-4 py-3  hover:bg-gray-50 cursor-pointer transition-colors">
                            <Search className="w-4 h-4 text-gray-400 ml-3"/>

                            <span className="flex-1 text-right text-gray-700 text-sm font-medium" dir="rtl">
                                {result.title}
                            </span>

                            <ArrowLeft className="w-4 h-4 text-gray-400 mr-3"/>
                        </Link>
                    ))}
                </div>
                }
            </div>
        </>
    );
}
