'use client'

import {useSearch} from "@/lib/hooks/useSearch";
import SearchMobileInput from "@/components/shop/mainPage/SearchMobileInput";
import SearchResults from "@/components/common/SearchResults";

type Props = {
    open: boolean;
    onClose: () => void;
}

export default function SearchMobileWrapper({onClose}: Props) {
    const {productParams, setProductParams, paginatedQuery} = useSearch();

    return (
        <>
            <SearchMobileInput
                open={true}
                onClose={onClose}
                setProductParams={setProductParams}
                value={productParams.search || ""}
            />
            <SearchResults
                isLoading={paginatedQuery.isLoading}
                searchValue={productParams.search}
                products={paginatedQuery.data?.data.products || []}
            />
        </>
    );
}
