
const SpecialPrice = ({price, className}: {price: number, className?: string}) => {
    return (
        <div className={`relative inline-block text-gray-400 font-bold rtl ` + className}>
            <span>{price.toLocaleString()} تومان</span>
            <div className="absolute left-0 top-1/2 w-full border-t border-gray-500 rotate-[-10deg]"></div>
        </div>
    )
}

export default SpecialPrice