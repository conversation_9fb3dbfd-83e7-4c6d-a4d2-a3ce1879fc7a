import {useState, useEffect, ReactNode} from "react";

type Props = {
    remainingTime?: number,
    onComplete?: () => void,
    renderer?: (second: number, minute: number) => ReactNode
    renderWhenCompleted?: React.ReactNode | null
}

const CountdownTimer = ({remainingTime, onComplete, renderer, renderWhenCompleted = null}: Props) => {
    const [timeLeft, setTimeLeft] = useState<number | undefined>();

    useEffect(() => {
        setTimeLeft(remainingTime);
    }, [remainingTime]);

    useEffect(() => {
        if (timeLeft === undefined) return;

        if (timeLeft <= 0) {
            if (onComplete) onComplete();
            return;
        }

        const interval = setInterval(() => {
            setTimeLeft((prev) => {
                const preValue = prev! - 1
                if (preValue === 0) {
                    clearInterval(interval)
                }
                return preValue
            });

        }, 1000);

        return () => clearInterval(interval);
    }, [timeLeft, onComplete]);

    if (timeLeft === undefined || timeLeft === null) return null;

    const minutes = Math.floor(timeLeft / 60);
    const secs = timeLeft % 60;

    if (timeLeft === 0) {
        return renderWhenCompleted;
    }

    if (renderer) return renderer(secs, minutes)

    return (
        <span
            className='text-muted-foreground'>
        {String(minutes).padStart(2, "0")}:{String(secs).padStart(2, "0")}
    </span>

    )

};

export default CountdownTimer;