import {ReactNode, useState} from "react";
import {
    AlertDialog, AlertDialogAction, AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription, AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger
} from "../UI/alert-dialog";

type Props = {
    title?: string,
    children: ReactNode,
    description: string,
    onConfirm?: () => void
}

export default function DialogModal({children, title, description, onConfirm}: Props) {
    const [open, setOpen] = useState(false);

    const handleClose = () => {
        setOpen(false);
    };

    const handleConfirm = () => {
        if (onConfirm) onConfirm();
        handleClose();
    };

    return (
        <AlertDialog open={open} onOpenChange={setOpen}>
            <AlertDialogTrigger asChild>
                {children}
            </AlertDialogTrigger>
            <AlertDialogContent dir="rtl" className="!text-right">
                <AlertDialogHeader>
                    <AlertDialogTitle
                        className='text-right text-base text-[#212121] font-bold'>{title || ' تایید !'}</AlertDialogTitle>
                    <AlertDialogDescription className='text-right text-[#596068] px-4'>
                        <span className='py-4'>{description}</span>
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogCancel onClick={handleClose} className='border mx-2'>لغو</AlertDialogCancel>
                    <AlertDialogAction onClick={handleConfirm}
                                       className='bg-primary text-white mx-2'>ادامه</AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    )
}
