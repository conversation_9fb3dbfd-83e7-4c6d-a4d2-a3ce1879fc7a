import Image from "next/image";
import ProductImage from "@/public/assets/images/car-test.png"
import CartIcon from '@/public/assets/images/cart-add.webp'

const ShoppingCard = () => {
  return (
    <div className="relative ali bg-white rounded-3xl shadow p-4 w-80 min-h-96 h-auto">
          <Image
            src={ProductImage}
            alt="Product"
            className="w-full rounded-xl"
          />
          <div className="flex items-center mt-3">
            {/* <span className="bg-yellow-500 text-black px-2 py-1 rounded-lg text-sm font-bold">4/9</span> */}
            <p className="text-gray-700 text-base">عنوان محصول در اینجا قرار می‌گیرد</p>
          </div>

          <div className="flex flex-row-reverse items-center gap-2 absolute right-4 bottom-3">
            <span className="text-gray-400 text-base">تومان</span>
            <span className="text-black text-lg font-bold ml-2">3,029,000</span>
          </div>
          {/* <span className="w-12 h-16 rounded-full bg-secondary-grey p-1 absolute left-3 -bottom-5"></span> */}
          <div className="w-16 h-16 rounded-full border border-orange-100 p-0.5 absolute left-10 -bottom-5">
            <div className="w-full h-full flex items-center justify-center rounded-full bg-orange-300 p-1">
              <Image src={CartIcon} alt="Product"
                className="mx-auto" />
            </div>
          </div>
        </div>
  )
}

export default ShoppingCard