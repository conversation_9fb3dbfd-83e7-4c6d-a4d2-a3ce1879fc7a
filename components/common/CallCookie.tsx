"use client"
import { SetNaghlieCookie } from '@/actions/other.action'
import React, { useEffect, useState } from 'react'
import Container from './Container'
import InquiryResultWrapper from './InquiryResultWrapper'
import { cn } from '@/lib/utils'
import { useAuth } from '@/lib/hooks/useAuth'

interface CallCookieProps {
  token?: string
  traceNumber: string
  isReInquiry: boolean
}
const CallCookie: React.FC<CallCookieProps> = ({ token, traceNumber, isReInquiry }) => {
  const { reFetchUser } = useAuth()
  const [isReady, setIsReady] = useState(false)

  useEffect(() => {
    async function setUserCookie() {
      try {
        if (token) {
          await SetNaghlieCookie(token)
        }
        await reFetchUser()
      } finally {
        setIsReady(true)
      }
    }

    setUserCookie()
  }, [])

  if (!isReady) {
    return (
      <div className="flex justify-center items-center h-[90vh]">
      <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin" />
    </div>
    )
  }

  return (
    <Container className={cn('!mb-24 ')}>
      <InquiryResultWrapper token={token} traceNumber={traceNumber} isReInquiry={isReInquiry} />
    </Container>
  )
}

export default CallCookie
