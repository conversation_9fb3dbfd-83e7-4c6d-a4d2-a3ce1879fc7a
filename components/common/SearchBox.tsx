"use client"
import { Search } from "lucide-react";
import clsx from "clsx";

interface SearchBoxProps {
  className?: string;
  placeholder?: string;
  onSearch?: (value: string) => void;
  buttonText?: string;
}

const SearchBox = ({
  className,
  placeholder = "دنبال چه چیزی هستین؟",
  onSearch,
  buttonText = "جستجو",
}: SearchBoxProps) => {
  return (
    <div className={clsx("relative max-md:w-[19rem] max-md:text-xs", className)}>
      <Search className="absolute bottom-4 max-md:bottom-[13px] right-2 text-gray-400 max-md:w-4" size={20} />
      <input
        type="text"
        placeholder={placeholder}
        className="w-full py-3.5 px-9 max-md:px-7 rounded-3xl text-sm max-md:text-xs border focus-visible:ring-0"
        onKeyDown={(e) => {
          if (e.key === "Enter" && onSearch) {
            onSearch((e.target as HTMLInputElement).value);
          }
        }}
      />
      <button
        type="button"
        className="bg-primary text-white px-3 py-2 max-md:py-2.5 text-sm max-md:text-xs rounded-3xl absolute bottom-[7px] max-md:bottom-1 left-3"
        // onClick={() => {
        //   const input = (e.currentTarget.parentElement?.querySelector("input") as HTMLInputElement | null);
        //   if (input && onSearch) onSearch(input.value);
        // }}
      >
        {buttonText}
      </button>
    </div>
  );
};

export default SearchBox;
