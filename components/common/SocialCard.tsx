import React from 'react';
import { ChevronRight } from 'lucide-react';

interface SocialCardProps {
  platformName: string;
  username: string;
  gradientFrom: string;
  gradientTo: string;
  icon: React.ReactNode;
}

const SocialCard: React.FC<SocialCardProps> = ({
  platformName,
  username,
  gradientFrom,
  gradientTo,
  icon
}) => {
  return (
    <div
      className="h-[5.4rem] w-full rounded-xl relative"
      style={{
        background: `linear-gradient(to right, ${gradientFrom}, ${gradientTo}), url('/assets/images/social-card-bg.webp') no-repeat bottom`,
        backgroundSize: "auto, contain",
        backgroundBlendMode: "overlay"
      }}
    >
      <div className="absolute bottom-6 left-6 flex items-center gap-5">
        <div className="">
          <div className="flex items-center gap-1 flex-row-reverse relative text-white text-sm">
            {platformName} 
            <div className='flex justify-start'>
              <ChevronRight className='text-white w-5' />
              <ChevronRight className='text-gray-200 absolute right-0 top-0 w-5' />
            </div>
          </div>
          <div className="text-sm text-white">
            {username}
          </div>
        </div>
        {icon}
      </div>
    </div>
  );
};

export default SocialCard;