import React from "react";

interface IconsProps extends React.SVGProps<SVGSVGElement> {}

 const RoadIcon = ({ ...props }: IconsProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      height="24px"
      viewBox="0 -960 960 960"
      width="24px"
      fill="#e3e3e3"
      {...props}
    >
      <path d="M160-160v-640h80v640h-80Zm280 0v-160h80v160h-80Zm280 0v-640h80v640h-80ZM440-400v-160h80v160h-80Zm0-240v-160h80v160h-80Z" />
    </svg>
  );
};

export default RoadIcon