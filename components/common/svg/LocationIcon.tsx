import React from "react";

interface LocationIconProps {
    size?: string
    fill?: string
    className?: string
}
const LocationIcon:React.FC<LocationIconProps> = ({ size, fill, className }) => (
    <svg
      width={ size || "44.215"}
      height={ size || "44.215"}
      className={className || ""}
      viewBox="0 0 44.215 48.75"
      fill={fill || "none"}
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <linearGradient id="location-gradient" x1="0.5" y1="1" x2="1.724" y2="0.5" gradientUnits="objectBoundingBox">
          <stop offset="0" stopColor="#EFF0F2" stopOpacity="0.412" />
          <stop offset="1" stopColor="#9DA5B0" stopOpacity="0.271" />
        </linearGradient>
      </defs>
  
      <path
        d="M7 1.662A7.026 7.026 0 001.662 7h.411A6.623 6.623 0 017 2.073V1.662m3 0v.411A6.623 6.623 0 0114.933 7h.411A7.026 7.026 0 0010 1.662M1.662 10A7.026 7.026 0 007 15.344v-.411A6.623 6.623 0 012.073 10h-.411m13.271 0A6.623 6.623 0 0110 14.933v.411A7.026 7.026 0 0015.344 10h-.411M8.5 0a8.5 8.5 0 018.5 8.5H13.6A5.1 5.1 0 008.5 3.4V0ZM0 8.5H3.4A5.1 5.1 0 008.5 13.6v3.4A8.5 8.5 0 010 8.5Zm13.6 0h3.4a8.5 8.5 0 01-8.5 8.5V13.6a5.1 5.1 0 005.1-5.1Z"
        transform="translate(13.605 13.605)"
        fill="url(#location-gradient)"
      />
      <path
        d="M40.814 21.856c0 5-2.913 10.916-6.932 15.674a30.323 30.323 0 01-6.291 5.728 10.755 10.755 0 01-5.483 2.092v3.4a14.006 14.006 0 007.351-2.65 33.7 33.7 0 007.022-6.375c4.271-5.057 7.735-11.735 7.735-17.869ZM22.108 45.349a10.755 10.755 0 01-5.483-2.092 30.324 30.324 0 01-6.291-5.728C6.315 32.772 3.4 26.853 3.4 21.856H0c0 6.134 3.464 12.812 7.735 17.868a33.7 33.7 0 007.022 6.375 14.007 14.007 0 007.351 2.65ZM3.4 21.856A18.582 18.582 0 0122.108 3.4V0A21.983 21.983 0 000 21.856ZM22.108 3.4A18.582 18.582 0 0140.814 21.856h3.4A21.983 21.983 0 0022.108 0Z"
        fill="url(#location-gradient)"
      />
    </svg>
  );
  
  export default LocationIcon;
  