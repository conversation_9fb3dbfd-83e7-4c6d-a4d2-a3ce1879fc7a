import { SVGProps } from 'react';

const WalletIcon = (props: SVGProps<SVGSVGElement>) => (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">
    <g id="Huge-icon_finance_and_payment_solid_wallet"  transform="translate(-2 -2)">
      <g id="wallet" transform="translate(2 2)">
        <path id="Combo_shape"  d="M10,0h6a4,4,0,0,1,3.709,2.5H6.291A4,4,0,0,1,10,0ZM0,10v4H4a2,2,0,0,0,0-4ZM20,4H4A4,4,0,0,0,0,8v.5H4a3.5,3.5,0,0,1,0,7H0V16a4,4,0,0,0,4,4H16a4,4,0,0,0,4-4Z" fill="#5e646b" fillRule="evenodd"/>
      </g>
    </g>
  </svg>
  
);

export default WalletIcon;
