import Image from "next/image";

type Props = {
    width?: number
    height?: number
}

export default function TickMarkCircleIcon({width, height}: Props) {
    return (
        <div
            className='w-[30px] h-[30px] rounded-full bg-green-500 border border-green-500 flex justify-center items-center'>
            <Image src='/assets/images/check mark- circle.svg' width={20} height={20} alt='car inquiry'/>
        </div>
    );
}
