
type IconProps = {
  size?: number;
  className?: string;
};

const InstagramIcon: React.FC<IconProps> = ({ size = 35.5, className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 35.5 35.5"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <defs>
      <linearGradient id="grad" x1="0.5" x2="0.5" y2="1">
        <stop offset="0" stopColor="#fff" />
        <stop offset="1" stopColor="#fff" stopOpacity="0.702" />
      </linearGradient>
    </defs>
    <path
      fill="url(#grad)"
      fillRule="evenodd"
      d="M8.875 0A8.875 8.875 0 0 0 0 8.875v17.75A8.875 8.875 0 0 0 8.875 35.5h17.75A8.875 8.875 0 0 0 35.5 26.625V8.875A8.875 8.875 0 0 0 26.625 0ZM28.4 8.875A1.775 1.775 0 1 0 26.625 7.1a1.775 1.775 0 0 0 1.775 1.775ZM26.625 17.75A8.875 8.875 0 1 1 17.75 8.875a8.875 8.875 0 0 1 8.875 8.875ZM17.75 23.075a5.325 5.325 0 1 0-5.325-5.325 5.325 5.325 0 0 0 5.325 5.325Z"
    />
  </svg>
);

export default InstagramIcon;
