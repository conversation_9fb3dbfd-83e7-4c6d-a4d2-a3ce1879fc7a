import React from "react";

type UndoCircleIconProps = React.SVGProps<SVGSVGElement>;

const UndoCircleIcon: React.FC<UndoCircleIconProps> = ({
  width = 18,
  height = 18,
  fill = "none",
  ...props
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 17.875 17.875"
    width={width}
    height={height}
    fill={fill}
    {...props}
  >
    <path
      d="M1.063 2.979l-.17.666a.688.688 0 01-.518-.666ZM.375.688a.688.688 0 011.375 0ZM3.033 2.771A.688.688 0 012.694 4.1ZM.022 5.9a.688.688 0 111.332-.343ZM4.239 7.792A2.979 2.979 0 007.218 4.813H8.593A4.354 4.354 0 014.239 9.167ZM7.218 4.813A2.979 2.979 0 004.239 1.833V.458A4.354 4.354 0 018.593 4.813ZM4.239 1.833a2.978 2.978 0 00-2.581 1.49L.468 2.635A4.353 4.353 0 014.239.458ZM.375 2.979V.688H1.75V2.979Zm.857-.666l1.8.458L2.694 4.1l-1.8-.458Zm.121 3.245A2.981 2.981 0 004.239 7.792V9.167A4.356 4.356 0 01.022 5.9Z"
      transform="translate(4.583 4.125)"
      fill="currentColor"
    />
    <path
      d="M8.938 16.5A7.563 7.563 0 011.375 8.938H0a8.937 8.937 0 008.938 8.938ZM16.5 8.938A7.563 7.563 0 018.938 16.5v1.375a8.937 8.937 0 008.938-8.937ZM8.938 1.375A7.563 7.563 0 0116.5 8.938h1.375A8.937 8.937 0 008.938 0ZM8.938 0A8.937 8.937 0 000 8.938H1.375A7.562 7.562 0 018.938 1.375Z"
      fill="currentColor"
    />
  </svg>
);

export default UndoCircleIcon;
