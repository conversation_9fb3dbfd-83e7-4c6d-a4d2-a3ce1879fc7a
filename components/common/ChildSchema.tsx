import React from "react";
import Script from "next/script";

interface ChildSchemaProps {
  schema: string;
  id: string;
}

const ChildSchema: React.FC<ChildSchemaProps> = ({ schema, id }) => {
  let pageSchema: unknown;

  try {
    pageSchema = JSON.parse(schema);
  } catch (error) {
    console.error(`Invalid JSON schema in ChildSchema (id: ${id}):`, error);
    return null; 
  }

  if (!pageSchema || typeof pageSchema !== "object") return null;

  return (
    <Script
      id={`jsonld-${id}`}
      type="application/ld+json"
      strategy="afterInteractive"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(pageSchema) }}
    />
  );
};

export default ChildSchema;
