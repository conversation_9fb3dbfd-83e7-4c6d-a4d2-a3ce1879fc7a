"use client";
import { useEffect } from "react";
import { usePathname, useSearchParams } from "next/navigation";
import page from "@/app/(dashboard)/dashboard/page";
import CookieService from "@/lib/services/cookie-service";
import { trackUTMFromServer } from "@/actions/other.action";
import { v1 as uuidv1 } from 'uuid';



const UtdTracker = () => {
    const pathname = usePathname();
    const searchParams = useSearchParams();
    
    useEffect(() => {
        async function udp() {
            const uTraceKey = "u_trace";
            let uTraceId = localStorage.getItem(uTraceKey);
        
            if (!uTraceId) {
              uTraceId = uuidv1();
              localStorage.setItem(uTraceKey, uTraceId);
            }
            const fullURL = window.location.href;
            const response = await trackUTMFromServer(fullURL, uTraceId!); // dadash uuid null nis be khoda
            console.log(response);
            //     const cookies = document.cookie.split("; ").filter(Boolean);
            // const cookieObj: Record<string, string> = {};

            // cookies.forEach((cookie) => {
            //     const [key, value] = cookie.split("=");
            //     if (key && value) {
            //         cookieObj[key] = decodeURIComponent(value);
            //     }
            // });           
            
            // const response = await fetch("https://dl.khodrox.com/api/v1/utm-conversion", {
            //     method: "POST",
            //     headers: {
            //         "Content-Type": "application/json",
            //         "UTM-DATA": JSON.stringify(cookieObj || null),
            //     },
            //     body: JSON.stringify({
            //         utm: fullURL,
            //         page: fullURL
            //     }),
            // })
            // const data = await response.json()
            // console.log(data);
            
            
            // if (data?.data?.utm) {
            //     const expires = new Date();
            //     expires.setTime(expires.getTime() + 2 * 24 * 60 * 60 * 1000); 
            //     Object.entries(data.data.utm).forEach(([key, value]) => {
            //         document.cookie =
            //             key +
            //             "=" +
            //             encodeURIComponent(value as string) +
            //             "; path=/; expires=" +
            //             expires.toUTCString();
            //     });
            // }

        }
        udp();
    }, [pathname, searchParams]); 

    return null;
};

export default UtdTracker;
