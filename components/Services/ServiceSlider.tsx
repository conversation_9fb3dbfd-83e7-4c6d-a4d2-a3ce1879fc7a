'use client'

import {Swiper, SwiperSlide} from 'swiper/react';
import 'swiper/css';
import Image from 'next/image';
import Link from "next/link";
import {Navigation, Pagination} from "swiper/modules";
import {CAR_TICKETS_PATH, MOTOR_TICKETS_PATH} from "@/lib/routes";
import CustomButton from "@/components/UI/CustomButton";
import {ChevronsLeft} from "lucide-react";

export default function ServiceSlider() {

    const slides = [
        {
            src: '/assets/images/banner-1.png',
            link: CAR_TICKETS_PATH,
            content: (<div className='flex flex-col gap-y-3'>
                <p className='text-white'>
                    <span className='font-light text-sm'>استعلام خلافی </span>
                    <span className='font-bold'>خودرو</span>
                </p>
                <div className='flex items-center justify-center gap-x-1'>
                    <span className='text-white text-xs font-light'>برای استعلام کلیک کنید</span>
                    <CustomButton className='!px-2 !py-1 bg-white w-fit'>
                        <span className='text-[10px] text-gray-700'>استعلام</span>
                        <ChevronsLeft size={8} className='text-[#505760]'/>
                    </CustomButton>
                </div>
            </div>)
        },
        // {
        //     src: '/assets/images/banner-2.png',
        //     link: '#',
        //     content: (<div className='flex flex-col gap-y-3 pr-5'>
        //         <p className='text-white'>
        //             <span className='font-light text-xl'>خرید بیمه شخص ثالث </span>
        //         </p>
        //         <div className='flex items-center justify-center gap-x-1'>
        //             <span className='text-white text-xs font-light'>برای استعلام و خرید کلیک کنید</span>
        //             <CustomButton className='!px-1 !py-0 bg-white w-fit'>
        //                 <span className='text-[10px] text-[#F4A859]'>خرید بیمه</span>
        //                 <ChevronsLeft size={8} className='text-[#F4A859]'/>
        //             </CustomButton>
        //         </div>
        //     </div>)
        // },
        {
            src: '/assets/images/banner-3.png',
            link: MOTOR_TICKETS_PATH,
            content: (<div className='flex flex-col gap-y-1 pr-5'>
                <div className='text-white flex flex-col gap-y-1'>
                    <span className='font-light text-sm'>استعلام خلافی </span>
                    <span className='font-light text-lg'>موتور سیکلت</span>
                </div>
                <div className='flex items-center justify-center gap-x-1'>
                    <span className='text-white text-xs font-light'>برای استعلام کلیک کنید</span>
                    <CustomButton className='!px-2 !py-1 bg-white w-fit'>
                        <span className='text-[10px] text-[#314D7F]'>استعلام</span>
                        <ChevronsLeft size={8} className='text-[#314D7F]'/>
                    </CustomButton>
                </div>
            </div>)
        },

    ];


    return (
        <div className="relative w-full h-full  md:px-[5%] lg:px-[10%]">
            <Swiper
                spaceBetween={10}
                breakpoints={{
                    0: {
                        slidesPerView: 1.2,
                        spaceBetween: 20,
                    },
                    624: {
                        slidesPerView: 2,
                        spaceBetween: 20,
                    },
                    1024: {
                        slidesPerView: 2,
                        spaceBetween: 50,
                    },
                }}
                modules={[Navigation, Pagination]}
                className="relative w-full h-full"
            >
                {slides.map((slide, index) => (
                    <SwiperSlide key={index}>
                        <Link href={slide.link} className='h-full w-full block'>
                            <Image
                                src={slide.src}
                                alt={`Service ${index + 1}`}
                                fill
                                className="w-full h-full"
                            />
                            <div className='absolute right-[5%] top-[25%]'>
                                {slide.content}
                            </div>
                        </Link>
                    </SwiperSlide>
                ))}
            </Swiper>
        </div>
    );
};
