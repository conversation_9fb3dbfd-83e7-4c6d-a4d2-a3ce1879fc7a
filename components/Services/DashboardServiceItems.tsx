
"use client"
import Link from "next/link";
import DisplayServiceIcon from "@/components/Services/DisplayServiceIcon";
import {
    CAR_TICKETS_PATH,
    CAR_VIOLATION_IMAGE_PATH,
    DRIVING_LICENSE_POINT_PATH,
    DRIVING_LICENSE_STATUS_PATH,
    MOTOR_TICKETS_PATH,
    PLATE_HISTORY_PATH,
} from "@/lib/routes";
import {ServiceColorVariantType, ServiceStatusType} from "@/lib/types/types";

//Icons
import KhalafiKhodroIcon from "@/components/common/svg/services/KhalafiKhodroIcon";
import KhalafiMotorIcon from "@/components/common/svg/services/KhalafiMotorIcon";
import ScreenViolationIcon from "@/components/common/svg/services/ScreenViolationIcon";
import CertificateStatusIcon from "@/components/common/svg/services/CertificateStatusIcon";
import InquiryMinusIcon from "@/components/common/svg/services/InquiryMinusIcon";
import InquiryHostoryIcon from "@/components/common/svg/services/InquiryHostoryIcon";
import { useServiceStatus } from "@/lib/providers/ServicesProvider";



interface ServiceItemProps {
    href: string;
    name: string;
    icon: React.ReactNode;
    colorVariant?: ServiceColorVariantType;
    status: ServiceStatusType;
}


const ServiceItem: React.FC<ServiceItemProps> = ({href, name, icon, colorVariant, status}) => {
    const isDisabled = status === 'DEACTIVE' 
    return (
        <li className="w-full">
            {isDisabled ? (
                <span className="flex flex-col items-center gap-y-3">
                    <DisplayServiceIcon variant={colorVariant} status={status}>{icon}</DisplayServiceIcon>
                    <span className="text-xs text-center px-1">{name}</span>
                </span>
            ) : (
                <Link href={href} className="flex flex-col items-center gap-y-3">
                    <DisplayServiceIcon variant={colorVariant} status={status}>{icon}</DisplayServiceIcon>
                    <span className="text-xs text-center px-1">{name}</span>
                </Link>
            )}
        </li>
    );
};

// const env = envConfig()


export default function DashboardServiceItems() {
     const { data } = useServiceStatus()
        
     const services_status = data?.data?.services || {}
        
    
    const services: ReadonlyArray<ServiceItemProps> = [
            {
                icon: <KhalafiKhodroIcon/>,
                href: CAR_TICKETS_PATH, colorVariant: "yellow",
                name: "خلافی خودرو",
                status: services_status.car_tickets
            },
            {
                icon: <KhalafiMotorIcon/>,
                href: MOTOR_TICKETS_PATH,
                colorVariant: "blue",
                name: "خلافی موتور سیکلت",
                status: services_status.motor_tickets
            },
            {
                icon: <ScreenViolationIcon/>,
                href: CAR_VIOLATION_IMAGE_PATH,
                colorVariant: "purple",
                name: "تصویر تخلفات رانندگی",
                status: services_status.car_violation_image
            },
            
            {
                icon: <CertificateStatusIcon/>,
                href: DRIVING_LICENSE_STATUS_PATH,
                colorVariant: "emerald",
                name: "وضعیت گواهینامه",
                status: services_status.driving_license_status
            },
            
            {
                icon: <InquiryMinusIcon/>,
                href: DRIVING_LICENSE_POINT_PATH,
                colorVariant: "red",
                name: "استعلام نمره منفی",
                status: services_status.driving_license_point
            },
            {
                icon: <InquiryHostoryIcon/>,
                href: PLATE_HISTORY_PATH,
                colorVariant: "indigo",
                name: "استعلام تاریخچه پلاک",
                status: services_status.plate_history
            },
            
           
        ];

    return (
        <ul className="w-full grid grid-cols-3 mt-8 gap-y-10 p-3">
            {services.map((service, index) => (
                <ServiceItem
                    key={index}
                    {...service}
                />
            ))}
        </ul>
    );
}
