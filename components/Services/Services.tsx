import DoubleArrowIcon from "@/components/common/svg/DoubleArrowIcon";
import ShowAllSection from "./ShowAllSection"
import CustomButton from "../UI/CustomButton"
import HomeSection from "@/components/Header/HomeSection";
import ServiceList from "@/components/Header/ServiceList";


const Services = () => {
    return (
        <HomeSection>
            <div className="w-full flex flex-col lg:flex-row items-center gap-x-3 gap-y-10">
                <div className="w-full lg:w-1/3">
                    <ShowAllSection>
                        <div className='hidden lg:block'>
                            <CustomButton bgColor="bg-[#B6BEC9]"
                                          className="flex px-3  gap-2 w-30 md:mt-2 justify-center rounded-3xl whitespace-nowrap text-xs cursor-not-allowed"> مشاهده
                                همه <DoubleArrowIcon/>
                            </CustomButton>
                        </div>
                    </ShowAllSection>
                </div>
                <div className="w-full lg:w-2/3">
                    <ServiceList/>
                </div>
            </div>
        </HomeSection>

    )
}

export default Services