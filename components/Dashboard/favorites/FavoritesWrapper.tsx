"use client"
import { getFavorites } from '@/actions/favorites.action';
import { ChevronDown, FilterIcon, Loader2, SlidersHorizontal } from 'lucide-react';
import React, { useState } from 'react'
import FavoritesPageList from './FavoritesPageList';
import { FavoriteItem, FavoritesResponseData } from '@/lib/types/favorites.types';
import Pagination from '@/components/common/Pagination';

const FavoritesWrapper = ({ favorites }: { favorites: FavoritesResponseData }) => {
    const [activeFilter, setActiveFilter] = useState<'' | 'newest' | 'most_expensive' | 'cheapest'>('');
    const [loading, setLoading] = useState(false);
    const [favoriteProducts, setFavoriteProducts] = useState<FavoriteItem[]>(favorites?.products || [])
    // pagnation statses
    const [currentPage, setCurrentPage] = useState(favorites.pagination.current_page);
    const [lastPage, setLastPage] = useState(favorites.pagination.last_page);
    const limit = favorites.pagination.limit


    const filters: { label: string; value: '' | 'newest' | 'most_expensive' | 'cheapest' }[] = [
        { label: 'همه', value: '' },
        { label: 'جدیدترین', value: 'newest' },
        { label: 'گرانترین', value: 'most_expensive' },
        { label: 'ارزانترین', value: 'cheapest' },
    ];

    const handleFilterClick = async (status: typeof activeFilter) => {
        setActiveFilter(status)
        fetchFavorites(1, status) // returns to first page when filter changes
    };


    const fetchFavorites = async (page: number, sort: string) => {
        setLoading(true);
        const res = await getFavorites(page, limit, sort);
        if (res.success) {
            setFavoriteProducts(res.data.products);
            setCurrentPage(res.data.pagination.current_page);
            setLastPage(res.data.pagination.last_page);
        }
        setLoading(false);
    };
    

    return (

        <div className="p-5">
            <div className="flex justify-between items-center w-full">
                <div className="flex flex-col md:flex-row gap-3 md:gap-5 items-center h-auto md:h-10 max-md:px-3 ">
                    <div className="flex items-center gap-2 max-md:hidden">
                        <SlidersHorizontal />
                        <span>مرتب سازی:</span>
                    </div>



                    <div className="hidden md:flex md:gap-8 h-full">
                        {filters.map(({ label, value }) => {
                            const isActive = activeFilter === value;
                            return (
                                <button
                                    key={value}
                                    onClick={() => handleFilterClick(value)}
                                    className={`transition-all ${isActive ? 'text-primary border-b-2 border-primary' : ''}`}
                                >
                                    {label}
                                    {/* <span className={`mr-2 rounded-full text-sm px-2 border ${isActive ? 'bg-[#F7BC06] text-black' : 'bg-[#F9FAFB] text-gray-500'}`}>
                                        {counts[countKey]}
                                    </span> */}
                                    
                                </button>
                            );
                        })}
                    </div>

                    {/* mobile version */}
                    <div className="w-full md:hidden">
                        <div className="relative w-full w-md">

                            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                                <FilterIcon className="w-5 h-5 text-gray-400" />
                            </div>

                            <select
                                value={activeFilter}
                                onChange={(e) => handleFilterClick(e.target.value as typeof activeFilter)}
                                className="w-full appearance-none border border-gray-300 rounded-xl py-2 pr-10 pl-8 text-sm text-gray-700 bg-white focus:ring-2 focus:ring-gray-200"
                            >
                                {filters.map(({ label, value }) => (
                                    <option key={value} value={value}>
                                        {label}
                                    </option>
                                ))}
                            </select>
                            <ChevronDown className='w-4 h-4 absolute left-2 top-1/2 transform -translate-y-1/2' />
                        </div>
                    </div>
                </div>



            </div>
            {loading ? (
                <div className="flex flex-1 justify-center items-center min-h-[300px]">
                    <Loader2 className="animate-spin w-10 h-10 text-primary" />
                </div>
            )
                :
                favoriteProducts.length ?
                    (
                                <>
                                    <FavoritesPageList
                                        favorites={favoriteProducts}
                                        setFavoriteProducts={setFavoriteProducts}
                                    />

                                    {lastPage > 1 && (
                                        <Pagination
                                            currentPage={currentPage}
                                            lastPage={lastPage}
                                            onPageChange={(page) => fetchFavorites(page, activeFilter)}
                                        />
                                    )}
                                </>
                    )
                    :
                    (
                        <div className="flex flex-1 justify-center items-center min-h-[300px] text-gray-400 text-lg">
                            هیچ محصولی برای نمایش وجود ندارد
                        </div>
                    )
            }
            {/* <FavoritesPageList favorites={favoriteProducts || []} setFavoriteProducts={setFavoriteProducts} /> */}


        </div>
    )
}

export default FavoritesWrapper