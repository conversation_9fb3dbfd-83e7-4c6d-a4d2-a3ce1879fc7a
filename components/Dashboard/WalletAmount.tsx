"use client"
import Image from 'next/image'
import Money from "@/public/assets/images/money.png"
import {useAuth} from '@/lib/hooks/useAuth'
import Link from 'next/link'

const WalletAmount = () => {
    const {userData} = useAuth()

    return (
        <div
            className='flex md:flex-col gap-7 px-4 py-3 items-center w-full  md:h-[60%] bg-white shadow-md rounded-3xl max-md:order-2  md:justify-center '>
            <div className='max-md:w-1/2 max-md:hidden'>
                <Image src={Money} alt='money'/>
            </div>
            <div
                className='text-center max-md:w-full flex md:flex-col gap-5 items-start max-md:h-full max-md:flex max-md:flex-col max-md:items-center md:h-full'>
                <h3 className='text-[#3EA75D] text-2xl mr-2'> {userData?.balance} <span
                    className='text-gray-600 text-xl'>تومان</span></h3>
                <h4 className='pt-2 mr-2 '> موجودی کیف پول من </h4>
                <Link href="/wallet"
                      className='bg-[#3EA75D] md:mt-3 max-md:w-full text-white py-3 px-7 rounded-[30px]'> افزایش
                    موجودی </Link>
            </div>
        </div>
    )
}

export default WalletAmount