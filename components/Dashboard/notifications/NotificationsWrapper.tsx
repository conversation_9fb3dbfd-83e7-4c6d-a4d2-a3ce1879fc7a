'use client';

import { useState } from "react";
import { getUserNotifications } from "@/actions/notifications.action";
import { NotificationItem, NotificationsData } from "@/lib/types/notifications.types";
import Pagination from "@/components/common/Pagination";
import { SlidersHorizontal, ChevronDown } from "lucide-react";
import { Loader2 } from "lucide-react";
import AlertIcon from "@/components/common/svg/AlertIcon";

const NotificationsWrapper = ({ notifications }: { notifications: NotificationsData }) => {
    const [notificationsList, setNotificationsList] = useState<NotificationItem[]>(notifications.data || []);
    const [loading, setLoading] = useState(false);

    const [currentPage, setCurrentPage] = useState(notifications.pagination.current_page);
    const [lastPage, setLastPage] = useState(notifications.pagination.last_page);

    const limit = 10;

    const [typeFilter, setTypeFilter] = useState<'all' | 'inquiries' | 'invoices' | 'transactions' | 'system' | 'low_stock'>('all');
    const [readFilter, setReadFilter] = useState<'all' | 'read' | 'unread'>('all');

    const typeOptions = [
        { label: 'همه پیام‌ها', value: 'all' },
        { label: 'استعلام‌ها', value: 'inquiries' },
        { label: 'فاکتورها', value: 'invoices' },
        { label: 'تراکنش‌ها', value: 'transactions' },
        { label: 'سیستمی', value: 'system' },
        { label: 'اتمام موجودی', value: 'low_stock' },
    ] as const;

    const readOptions = [
        { label: 'همه', value: 'all' },
        { label: 'خوانده‌شده', value: 'read' },
        { label: 'خوانده‌نشده', value: 'unread' },
    ] as const;

    const fetchNotifications = async (page: number, read: string, type: string) => {

        setLoading(true);
        const res = await getUserNotifications(
            page,
            limit,
            read !== 'all' ? read : '',
            type !== 'all' ? type : ''
        );

        if (res.success) {
            setNotificationsList(res.data.data);
            setCurrentPage(res.data.pagination.current_page);
            setLastPage(res.data.pagination.last_page);
        }

        setLoading(false);
    };

    const handleTypeFilterChange = (type: typeof typeFilter) => {
        setTypeFilter(type);
        fetchNotifications(1, readFilter, type);
    };

    const handleReadFilterChange = (read: typeof readFilter) => {
        setReadFilter(read);
        fetchNotifications(1, read, typeFilter);
    };

    return (
        <div className="p-5">
            {/* فیلترها */}
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                <div className="flex items-center gap-3 max-md:hidden">
                    <SlidersHorizontal />
                    <span>فیلتر نوع:</span>
                    <div className="flex gap-4">
                        {typeOptions.map(({ label, value }) => (
                            <button
                                key={value}
                                onClick={() => handleTypeFilterChange(value)}
                                className={`transition-all md:text-base text-sm ${typeFilter === value ? 'text-primary border-b-2 border-primary' : ''}`}
                            >
                                {label}
                            </button>
                        ))}
                    </div>
                </div>

                {/* <div className="flex items-center gap-3">
          <span>وضعیت خواندن:</span>
          <div className="flex gap-4">
            {readOptions.map(({ label, value }) => (
              <button
                key={value}
                onClick={() => handleReadFilterChange(value)}
                className={`transition-all text-sm ${readFilter === value ? 'text-primary border-b-2 border-primary' : ''}`}
              >
                {label}
              </button>
            ))}
          </div>
        </div> */}
            </div>

            {/* نسخه موبایل فیلترها */}
            <div className="md:hidden mt-4 space-y-3">
                <div className="relative">
                    <select
                        value={typeFilter}
                        onChange={(e) => handleTypeFilterChange(e.target.value as typeof typeFilter)}
                        className="w-full appearance-none border border-gray-300 rounded-xl py-2 px-4 text-sm text-gray-700 bg-white focus:ring-2 focus:ring-gray-200"
                    >
                        {typeOptions.map(({ label, value }) => (
                            <option key={value} value={value}>
                                {label}
                            </option>
                        ))}
                    </select>
                    <ChevronDown className="w-4 h-4 absolute left-2 top-1/2 transform -translate-y-1/2" />
                </div>

                        {/* read filters */}
                {/* <div className="relative">
                    <select
                        value={readFilter}
                        onChange={(e) => handleReadFilterChange(e.target.value as typeof readFilter)}
                        className="w-full appearance-none border border-gray-300 rounded-xl py-2 px-4 text-sm text-gray-700 bg-white focus:ring-2 focus:ring-gray-200"
                    >
                        {readOptions.map(({ label, value }) => (
                            <option key={value} value={value}>
                                {label}
                            </option>
                        ))}
                    </select>
                    <ChevronDown className="w-4 h-4 absolute left-2 top-1/2 transform -translate-y-1/2" />
                </div> */}
            </div>

            {/* لیست نوتیفیکیشن */}
            <div className="mt-6">
                {loading ? (
                    <div className="flex justify-center items-center min-h-[300px]">
                        <Loader2 className="animate-spin w-10 h-10 text-primary" />
                    </div>
                ) : notificationsList.length > 0 ? (
                    <>
                        {/* <FavoritesPageList
              favorites={notificationsList}
              setFavoriteProducts={setNotificationsList}
            /> */}
                        {/* <NotificationsList notifications={notificationsList} /> */}
                        <div className="flex flex-col gap-5">
                            {notificationsList.map((notification) => (
                                <div key={notification.id} >
                                    <div className="flex  items-center mb-5">
                                        <div className="flex justify-start items-center gap-3">
                                            <div className="h-full bg-primary rounded-full p-1">
                                                <AlertIcon className="w-6 h-6 text-white" />
                                            </div>
                                            <div className="md:text-base text-sm">
                                                <p className="mb-2"> {notification.title} </p>
                                            <div className=" flex items-center gap-5">
                                                {/* <p className="p-1 bg-primary w-1 rounded-full text-left"></p> */}
                                                <p> {notification.created_at} </p>
                                                <p className="md:text-base text-sm"> {notification.created_date_ago} </p>
                                            </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="flex justify-end ">
                                        <p className="md:text-base text-sm w-full bg-gray-200 p-3  rounded-xl text-justify leading-5">
                                            {notification.body}
                                        </p>
                                    </div>
                                </div>
                            ))}
                        </div>
                        {lastPage > 1 && (
                            <Pagination
                                currentPage={currentPage}
                                lastPage={lastPage}
                                onPageChange={(page) =>
                                    fetchNotifications(page, readFilter, typeFilter)
                                }
                            />
                        )}
                    </>
                ) : (
                    <div className="text-center text-gray-400 text-lg min-h-[300px] flex items-center justify-center">
                        هیچ پیامی برای نمایش وجود ندارد.
                    </div>
                )}
            </div>
        </div>
    );
};

export default NotificationsWrapper;
