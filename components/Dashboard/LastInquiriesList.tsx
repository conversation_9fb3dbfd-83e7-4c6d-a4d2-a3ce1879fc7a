import React from 'react'
import InquiryItem from './InquiryItem'
import { UserLastInquiriesProps } from './UserLastInquiries'

const inquiryList: { // test array list 
    id: string
    inquiry_type: "car" | "motor"
}[] = [
    {
        id:"1",
        inquiry_type: "car"
    },
    {
        id:"1",
        inquiry_type: "motor"
    },
    {
        id:"1",
        inquiry_type: "car"
    },
    {
        id:"1",
        inquiry_type: "motor"
    },
    {
        id:"1",
        inquiry_type: "car"
    },
]

const LastInquiriesList = ({inquiries}: UserLastInquiriesProps) => {
    return (
        <div className='mt-5 min-h-[31rem]'>
            {inquiries.map((item, index) => (
                <InquiryItem key={item.trace_number} inquiry={item} index={index} />
            ))}
        </div>
    );
}

export default LastInquiriesList