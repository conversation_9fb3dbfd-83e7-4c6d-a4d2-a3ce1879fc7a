/**
 * Component for a single payment detail row
 * Displays a label and value 
 */
const PaymentDetailRow: React.FC<{
  label: string;
  value?: string | number | React.ReactNode;
  className?: string;
}> = ({ label, value, className = '' }) => (
  <div className={`flex justify-between items-center ${className}`}>
    <span className="font-medium">{label}</span>
    <span className="text-gray-700">{value}</span>
  </div>
);
export default PaymentDetailRow