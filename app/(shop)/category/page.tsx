import "@/styles/styles.css"

import {ProductFilterOptions} from "@/lib/types/product.types";
import ShopCategoryComponent from "@/components/shop/ShopCategoryComponent";
// import {getProducts} from "@/lib/services/productService";
import {getCategories, getProducts} from "@/actions/product.action";
import {headers} from "next/headers";
import CategoryService from "@/lib/services/category.service";

type Props = {
    searchParams: Promise<ProductFilterOptions>
}

export default async function ShopMainPage({searchParams}: Props) {
    const {
        has_guarantee_only,
        in_stock_only = 'true',
        page,
        limit = 20,
        search,
        sort,
        max_price,
        min_price
    } = await searchParams

    const userAgent = (await headers()).get('user-agent') || ''
    const isMobile = /mobile/i.test(userAgent)

    const productFilter: ProductFilterOptions = {
        has_guarantee_only,
        page: page ? page : !isMobile ? 1 : undefined,
        limit,
        search,
        sort,
        max_price,
        min_price,
        in_stock_only
    }

    let productResponse, categoryResponse;

    try {
        [productResponse, categoryResponse] = await Promise.all([
            getProducts(productFilter),
            // CategoryService.getCategories(),
            getCategories()
        ]);
        console.log("_________________");
        
        console.log(productResponse, categoryResponse);
    } catch (error) {
        console.error("Error fetching products or categories:", error);
    }

    return (
        <div className='mb-10 min-h-[80vh] flex flex-col w-full md:p-5 mx-auto'>

            {/* <div className='grid grid-cols-8 gap-6 max-w-7xl mx-auto mt-6 h-20'>
        <div className='col-span-2 bg-white flex justify-between items-center gap-3 rounded-3xl px-2'>
          <span>فیلترها</span>
          <button className='bg-[#FF4A4A] text-white px-4 py-3 rounded-3xl flex flex-row-reverse gap-2 text-sm items-center'>پاک کردن <Trash2 /> </button>
        </div>
        <div className='bg-white col-span-6 gap-7 flex items-center px-3 rounded-3xl'>
          <div className='flex gap-3 items-center'>
            <SlidersHorizontal />
            مرتب سازی بر اساس
          </div>
          <ul className='flex justify-between gap-10'>
            <li>
              جدیدترین
            </li>
            <li>
              ارزان ترین
            </li>
            <li>
              گران ترین
            </li>
            <li>
              پرفروش ترین
            </li>
            <li>
              پر بازدید ترین
            </li>
          </ul>
          <span className='mr-20'>214 محصول</span>
        </div>

      </div> */}
            <ShopCategoryComponent
                data={productResponse}
                params={productFilter}
                isMobile={isMobile}
                categories={categoryResponse?.data || []}
            />
        </div>
    )
}

