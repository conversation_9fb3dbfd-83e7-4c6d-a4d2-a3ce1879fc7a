import OrderSummaryList from '@/components/shop/checkout/OrderSummaryList'
import AddressWrapper from '@/components/shop/paymentResult/AddressWrapper'
import OrderStatusDetails from '@/components/shop/paymentResult/OrderStatusDetails'
import OrderStatusHeader from '@/components/shop/paymentResult/OrderStatusHeader'
import CustomButton from '@/components/UI/CustomButton'
import { apiClient } from '@/lib/apiClient'
import { InvoiceResponse } from '@/lib/types/invoice.types'
import { ProductTransactionResponse } from '@/lib/types/transaction.types'


type PageProps = {
  searchParams: Promise<{
    payable_id?: string
  }>
}
const OrderStatusPage = async ({searchParams}: PageProps) => {
    // invoice_id == payable_id
    const {payable_id} = await searchParams
    // const orderStatusDetails: InvoiceResponse = await fetch(`https://shop-khodrox.liara.run/api/v1/invoices/${payable_id}`, {
    //     method: "GET",
    //     headers: {
    //         "Authorization": "matin_token",
    //         "Content-Type": "application/json",
    //         "X-Application-Token": "matin_token",
    //         "Accept": "application/json"
    //     }
    // })
    // .then(res => res.json())
    const orderStatusDetails: InvoiceResponse = await apiClient(`invoices/${payable_id}`, {
        base: "alt",
        method: "GET",
    })
    .then(res => res.json())
    console.log(orderStatusDetails);
    console.log("hhhh");
    
  return (
    <>
     <section className="min-h-screen max-md:overflow-hidden max-md:px-2 mb-10">
            <div className="max-w-5xl max-md:max-w-sm mx-auto max-md:pb-3 pb-5 mt-20 max-md:mt-8 p-3 max-md:p-1.5 bg-white rounded-3xl border border-gray-100 half-circle-payment-card">
               <OrderStatusHeader orderStatus={orderStatusDetails} />
                <div className="my-5 md:w-[95%] mx-auto md:px-3 max-md:px-1">
                    <AddressWrapper address={orderStatusDetails.data.address} />
                </div>
                
                <div className="md:w-[95%] mx-auto pt-10 mt-12 border-t-2 border-dashed max-md:px-1">
                    <h3 className="text-center text-xl">
                        خلاصه سفارش
                    </h3>
                    <OrderSummaryList items={orderStatusDetails.data.products} className="md:w-full max-md:text-xs" />

                </div>
                <div className="flex justify-between md:w-[95%] mx-auto mt-6 md:px-3">
                    <CustomButton disabled className="w-[48%] py-5 max-md:py-4 md:text-lg max-md:text-xs">
                        مشاهده فاکتور
                    </CustomButton>
                    <CustomButton className="w-[48%] py-5 max-md:py-4 md:text-lg max-md:text-xs bg-[#9DA5B0]">
                        خرید مجدد کالاها
                    </CustomButton>
                </div>
            </div>
        </section>
    </>
  )
}

export default OrderStatusPage