import PaymentPageClient from "@/components/shop/checkout/PaymentPageClient"
import { CartApiResponse } from "@/lib/context/cart-context"
import { getShoppingCart } from "@/lib/services/productService"
import { ArrowRight } from "lucide-react"
import Link from "next/link"
type PageProps = {
  searchParams: Promise<{
    address?: string;
    deliveryId?: string;
  }>
}
const CheckoutPaymentPage = async ({ searchParams }: PageProps) => {
  const {address, deliveryId} = await searchParams
  const OrderSummaryList:CartApiResponse = await getShoppingCart()
  console.log(address);
  
  return (
    <main className='container mx-auto mb-16'> 
    <div className='px-1 mt-8 flex items-center gap-2' >
         <Link href={'/checkout/shipping'} type='button' className='bg-white max-md:hidden rounded-lg px-4 py-1.5 flex items-center gap-3'>
           <ArrowRight className=' max-md:hidden' size={26} /> بازگشت
         </Link>
        </div>    
      <PaymentPageClient addressId={address} orderSummaryData={OrderSummaryList.data} deliveryId={deliveryId} />
    </main>
  )
}

export default CheckoutPaymentPage