import { getInquiryHistory } from "@/actions/inquiry.action"
import UserLastInquiries from "@/components/Dashboard/UserLastInquiries"

const InquiryHistoryPage = async () => {
    const response = await getInquiryHistory()

    const inquiries = response?.data ?? []
    return (
            <UserLastInquiries title="لیست استعلام ها" inquiries={inquiries} error={response.message} />       

    )
}

export default InquiryHistoryPage