export const dynamic = 'force-dynamic';
import DashboardAddressList from '@/components/Dashboard/addresses/DashboardAddressList';
import { UserAddressesResponse } from '@/lib/types/types';
import { getUserAddresses } from '@/lib/utils'


const UserAddressesPage = async () => {
    const response: UserAddressesResponse = await getUserAddresses();
    console.log(response)
    
  return (
    <div className='flex flex-col gap-4 bg-white md:p-6 max-md:p-3 rounded-2xl shadow-md min-h-96'>
        
           <DashboardAddressList addressList={response?.data || []} />
        
    </div>
  )
}

export default UserAddressesPage