
import { getTicketDepartment } from "@/actions/tickets.action"
import ChatPlusIcon from "@/components/common/svg/ChatPlusIcon"
import CreateTicketClient from "@/components/Dashboard/tickets/CreateTicketClient";
import { ApiResponse } from "@/lib/types/favorites.types";
import { Link } from "lucide-react"

export interface DepartmentData {
    id: number;
    title: string;
}

const CreateTicketPage = async () => {

    const departments:ApiResponse<DepartmentData[]> = await getTicketDepartment()
    console.log(departments);
    return (
        <div className="bg-white p-5 rounded-xl min-h-[20rem] shadow-md">
            <div className="flex items-center gap-3 h-[3rem]">
                <div className="bg-gradient-to-t from-[#F5F6F8] to-transparent h-full p-2 pt-2.5 rounded-b-full">
                    <ChatPlusIcon className="" />
                </div>
                <h1>
                    ارسال تیکت جدید
                </h1>
            </div>
            <div className="p-1 mt-5">
                <p className="bg-[#FFF5D8] p-2 border-2 border-[#F7BC06] border-dashed max-md:text-sm leading-8 rounded-xl">
                    لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ

                </p>

                <CreateTicketClient departments={departments?.data || []} />
            </div>
        </div>
    )
}

export default CreateTicketPage