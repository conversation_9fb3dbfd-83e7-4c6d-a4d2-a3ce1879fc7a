'use client'

import {useEffect} from 'react'
import Card from "@/components/common/Card";

export default function Error({
                                  error,
                                  reset,
                              }: {
    error: Error & { digest?: string }
    reset: () => void
}) {
    useEffect(() => {
    }, [error])

    return (
        <div className='h-screen w-full flex justify-center items-center'>
            <Card>
                <div className='w-full flex justify-center items-center flex-col'>
                    <h2>با عرض پوزش مشکلی در سایت پیش آمد</h2>
                    <button
                        onClick={
                            // Attempt to recover by trying to re-render the segment
                            () => reset()
                        }
                    >
                        تلاش مجدد
                    </button>
                </div>
            </Card>
        </div>
    )
}