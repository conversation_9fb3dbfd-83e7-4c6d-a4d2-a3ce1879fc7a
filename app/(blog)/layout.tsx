import ShopHeader from "@/components/Header/ShopHeader";
import ShopNavbar from "@/components/Header/ShopNavbar";
import Footer from "@/components/UI/Footer";
import type{ ReactNode } from "react";
import type { Metadata } from 'next';
import "@/styles/styles.css"
import Navbar from "@/components/Header/Navbar";


type Props = {
    children: ReactNode;
}

export default function layout({children}: Props) {
    return (
        <>
            <ShopNavbar />
            <div className="md:hidden">
                <Navbar />
            </div>
            {/* <ShopHeader /> */}
            {children}
            <Footer />            
        </>
    );
}
