import InquiryComponent from "@/components/inquiry/InquiryComponent";
import Faq from "@/components/InquiryStaticComponents/Faq";
import envConfig from "@/lib/config-env";
import ChildSchema from "@/components/common/ChildSchema";
import { PageContentResponse } from "@/lib/types/types";
import ArticleSection from "@/components/InquiryStaticComponents/ArticleSection";
import { getPageContent } from "@/lib/utils";


export async function generateMetadata() {
    const data = await getPageContent("plate-history");
    
    return {
        title: data.meta_title,
        description: data.meta_description,
    };
}

export const revalidate = 0

const isMotor: boolean = false
const withDetails: boolean | undefined = true


export default async function PlateHistoryPage() {
    const env = envConfig()
    const status = env.Services.LICENSE_PLATE_HISTORY_INQUIRY_SECTION
    const data: PageContentResponse = await getPageContent("plate-history")


    const { schema, description, faqs, title } = data
    return (
        <>
            {
                schema &&
                <ChildSchema
                    id="plate-history"
                    schema={schema}
                />
            }
            <InquiryComponent
                title={title || ""}
                isMotor={isMotor}
                withDetails={withDetails}
                status={status}
            />
            {/* <PlateHistoryAbout/> */}
            {
                description &&
                <ArticleSection description={description} />
            }
            {
                faqs &&
                <Faq faqs={faqs} className="mt-10 mb-10">
                </Faq>
            }
        </>
    );
}
