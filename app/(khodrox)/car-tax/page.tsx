import ChildSchema from "@/components/common/ChildSchema";
import InquiryComponent from "@/components/inquiry/InquiryComponent";
import ArticleSection from "@/components/InquiryStaticComponents/ArticleSection";
import Faq from "@/components/InquiryStaticComponents/Faq";
import { PageContentResponse } from "@/lib/types/types";
import { getPageContent } from "@/lib/utils";


export async function generateMetadata() {
    const data = await getPageContent("car-tax");

    return {
        title: data.meta_title,
        description: data.meta_description,
    };
}


const isMotor: boolean = false
const withDetails: boolean | undefined = true


export default async function CarTaxPage() {
    const data: PageContentResponse = await getPageContent("car-tax")
    const { schema, description, faqs, title } = data
    return (
        <>
            {
                schema &&
                <ChildSchema
                    id="car-tax"
                    schema={schema}
                />
            }
            <InquiryComponent title={title || ""} isMotor={isMotor} withDetails={withDetails} />
            {/* <CarTaxAboutService /> */}
            {
                description &&
                <ArticleSection description={description} />
            }
            {
                faqs &&
                <Faq faqs={faqs} className="mt-10 mb-10" />
            }

        </>
    );
}
