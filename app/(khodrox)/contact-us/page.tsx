import {PageDescription} from "@/components/common/PageDescription";
import Container from "@/components/common/Container";
import ContactUsCard from "@/components/contact-us/ContactUsCard";
import { getPageContent } from "@/lib/utils";
import { PageContentResponse } from "@/lib/types/types";

export async function generateMetadata() {
    const data = await getPageContent("contact-us");

    return {
        title: data.meta_title,
        description: data.meta_description,
        robots: 'noindex, nofollow'
    };
}


export default async function ContactUsPage() {
    const data: PageContentResponse = await getPageContent("contact-us")
    console.log(data);
    
    
        const { faqs, title } = data
    return (
        <Container>
            <div className='w-full max-w-[763px]'>
                <PageDescription
                    title={title || ""}
                    description=""
                />
                <div className='mt-5 md:mt-10'>
                    <ContactUsCard faqs={faqs} />
                </div>
            </div>
        </Container>
    );
}
