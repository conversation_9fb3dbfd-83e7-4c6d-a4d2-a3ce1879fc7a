'use client'

import { useRouter, useSearchParams } from 'next/navigation'
import { useEffect } from 'react'
import { postInquireVehicle } from '@/actions/inquiry.action'
import { InquireCarArgs } from '@/lib/types/action-types'


export default function ResultPage() {
    const router = useRouter()
    const searchParams = useSearchParams()

   useEffect(() => {
    const phoneNumber = searchParams.get('phoneNumber') || ''
    const nationalCode = searchParams.get('nationalCode') || ''
    const plateLeft = searchParams.get('plateLeft') || ''
    const plateMiddle = searchParams.get('plateMiddle') || ''
    const plateRight = searchParams.get('plateRight') || ''
    const plateAlphabet = searchParams.get('plateAlphabet') || ''
    const withDetails = searchParams.get('withDetails') === 'true'
    const type = searchParams.get('type') || 'car'

    const fetchData = async () => {
        
        const payload: InquireCarArgs = {
            type,
            plaque: {
                left: plateLeft,
                mid: plateMiddle,
                right: plateRight,
                alphabet: plateAlphabet
            },
            inquiry: true,
            redirect: true
        }

        if (withDetails) {
            payload.details = {
                phone: phoneNumber,
                national_id: nationalCode
            }
        }

        const inquiryResponse = await postInquireVehicle(payload)

        if (inquiryResponse.success) {
            router.push(`tickets-result?traceNumber=${inquiryResponse.data?.trace_number}&reInquiry=false&isMotor=${type === 'motor'}`)
        } else {
            router.push(`/wallet?inquiry=true&message=${encodeURIComponent(inquiryResponse.message || '')}`)
        }
    }

    fetchData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
}, [])


    return (
        <div className="flex flex-col gap-5 justify-center items-center h-[90vh]">
                <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin" />
                <div className='flex flex-col items-center gap-3'>
                    <p> در حال دریافت استعلام ... </p>                    
                </div>
            </div>
    )
}
