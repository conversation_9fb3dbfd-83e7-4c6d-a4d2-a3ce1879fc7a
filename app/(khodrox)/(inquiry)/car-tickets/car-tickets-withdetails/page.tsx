import ChildSchema from "@/components/common/ChildSchema";
import InquiryComponent from "@/components/inquiry/InquiryComponent";
import ArticleSection from "@/components/InquiryStaticComponents/ArticleSection";
// import CarTicketsWithDetailsAboutService from "@/components/InquiryStaticComponents/CarTicketsWithDetailsAboutService";
import Faq from "@/components/InquiryStaticComponents/Faq";
import { PageContentResponse } from "@/lib/types/types";
import { getPageContent } from "@/lib/utils";
// import env from "@/lib/config-env";


export async function generateMetadata() {
    const data = await getPageContent("car-tickets/car-tickets-withdetails");

    return {
        title: data.meta_title,
        description: data.meta_description,
    };
}



const isMotor: boolean = false
const withDetails: boolean | undefined = true
export default async function CarTicketsWithDetailsPage() {

    const data: PageContentResponse = await getPageContent("car-tickets/car-tickets-withdetails")

    const { schema, description, faqs, title } = data
    // const relevantFAQs = data.faqs


    // const jsonLdData = {
    //     "@context": "https://schema.org",
    //     "@type": "Service",
    //     "serviceType": "استعلام و پرداخت خلافی خودرو همراه با جزئیات",
    //     "url": "https://khodrox.com/car-tickets/car-tickets-withdtails",
    //     "provider": {
    //         "@type": "Organization",
    //         "name": "خودراکس",
    //         "url": "https://khodrox.com"
    //     },
    //     "name": "استعلام و پرداخت خلافی خودرو با شماره موبایل و کد ملی-راهور120",
    //     "description": "استعلام خلافی خودرو با جزئیات (با کد ملی) در سامانه خودراکس! امکان پرداخت آنی، گرفتن خلافی خودرو با شماره پلاک و شماره موبایل",
    //     "serviceOutput": "لیست کامل خلافی‌های خودرو با اطلاعات کامل (تاریخ، مکان، نوع تخلف) و پرداخت",
    //     "priceRange": ""
    // };
    return (
        <>
            {
                schema &&
                <ChildSchema
                    id="car-tickets-withdetails"
                    schema={schema}
                />
            }
            <InquiryComponent
                title={title || ""}
                isMotor={isMotor}
                withDetails={withDetails}
            />
            {/* <CarTicketsWithDetailsAboutService description={data.description} /> */}
            {
                description &&
                <ArticleSection description={description} />
            }
            {/* <OtherServices/> */}
            {
                faqs &&
                <Faq faqs={faqs} className="mt-10 mb-10" />
            }
        </>
    );
}
