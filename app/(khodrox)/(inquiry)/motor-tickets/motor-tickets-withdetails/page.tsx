import ChildSchema from "@/components/common/ChildSchema";
import InquiryComponent from "@/components/inquiry/InquiryComponent";
import ArticleSection from "@/components/InquiryStaticComponents/ArticleSection";
import Faq from "@/components/InquiryStaticComponents/Faq";
import { PageContentResponse } from "@/lib/types/types";
import { getPageContent } from "@/lib/utils";


export async function generateMetadata() {
    const data = await getPageContent("motor-tickets/motor-tickets-withdetails");

    return {
        title: data.meta_title,
        description: data.meta_description,
    };
}


const isMotor: boolean = true
const withDetails: boolean | undefined = true



export default async function MotorTicketsWithDetailsPage() {
    const data: PageContentResponse = await getPageContent("motor-tickets/motor-tickets-withdetails")
    const { schema, description, faqs, title } = data

    return (
        <>
            {
                schema &&
                <ChildSchema
                    id="motor-tickets-withdetails"
                    schema={schema}
                />
            }
            <InquiryComponent title={title || ""} isMotor={isMotor} withDetails={withDetails} />
            {/* <MotorWithDetails /> */}
            {
                description &&

                <ArticleSection description={description} />
            }
            {
                faqs &&
                <Faq faqs={faqs} className="mt-10 mb-10" />
            }
        </>
    );
}
