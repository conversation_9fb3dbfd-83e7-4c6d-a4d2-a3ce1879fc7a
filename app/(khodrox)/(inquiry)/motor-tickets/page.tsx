import InquiryComponent from "@/components/inquiry/InquiryComponent";
import Faq from "@/components/InquiryStaticComponents/Faq";
import envConfig from "@/lib/config-env";
import MotherSchema from "@/components/common/MotherSchema";
import ArticleSection from "@/components/InquiryStaticComponents/ArticleSection";
import { PageContentResponse } from "@/lib/types/types";
import { getPageContent } from "@/lib/utils";


export async function generateMetadata() {
    const data = await getPageContent("motor-tickets");

    return {
        title: data.meta_title,
        description: data.meta_description,
    };
}

export const revalidate = 0



const isMotor: boolean = true
const withDetails: boolean | undefined = undefined
export default async function MotorTicketsPage() {
    const env = envConfig()
    const status = env.Services.MOTORCYCLE_VIOLATION_SECTION
    
    const data: PageContentResponse = await getPageContent("motor-tickets")

    const { schema, description, faqs, title } = data

    return (
        <>
            {
                schema &&
                <MotherSchema
                    id="motor-tickets"
                    schema={schema}
                />
            }
            <InquiryComponent
                title={title || ""}
                isMotor={isMotor}
                withDetails={withDetails}
                status={status}
            />
            {/* <MotorTicketsAboutService /> */}
            {
                description &&
                <ArticleSection description={description} />
            }
            {
                faqs &&
                <Faq faqs={faqs} className="mt-10 mb-10" />
            }
        </>
    );
}
