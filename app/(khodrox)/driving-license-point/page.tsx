import DrivingLicenseComponent from "@/components/DrivingLicensePoint/DrivingLicenseComponent";
import Faq from "@/components/InquiryStaticComponents/Faq";
import envConfig from "@/lib/config-env";
import ChildSchema from "@/components/common/ChildSchema";
import { PageContentResponse } from "@/lib/types/types";
import ArticleSection from "@/components/InquiryStaticComponents/ArticleSection";
import { getPageContent } from "@/lib/utils";

export async function generateMetadata() {
    const data = await getPageContent("driving-license-point");

    return {
        title: data.meta_title,
        description: data.meta_description,
    };
}


export const revalidate = 0


export default async function DrivingLicensePointPage() {
    const env = envConfig()
    const status = env.Services.NEGATIVE_SCORE_INQUIRY_SECTION
    const data: PageContentResponse = await getPageContent("driving-license-point")
    const { schema, description, faqs, title } = data
    return (
        <>
            {
                schema &&
                <ChildSchema
                    id="driving-license-point"
                    schema={schema}
                />
            }

            <DrivingLicenseComponent title={title || ""} status={status} />
            {/* <DrivingLicensePoint /> */}
            {
                description &&
                <ArticleSection description={description} />
            }
            {
                faqs &&
                <Faq faqs={faqs} className="mt-10 mb-10" />
            }
        </>
    );
}
