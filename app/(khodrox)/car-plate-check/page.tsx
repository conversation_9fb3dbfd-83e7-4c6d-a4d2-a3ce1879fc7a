import ChildSchema from "@/components/common/ChildSchema";
import InquiryComponent from "@/components/inquiry/InquiryComponent";
import ArticleSection from "@/components/InquiryStaticComponents/ArticleSection";
import Faq from "@/components/InquiryStaticComponents/Faq";
import { PageContentResponse } from "@/lib/types/types";
import { getPageContent } from "@/lib/utils";


export async function generateMetadata() {
    const data = await getPageContent("car-plate-check");

    return {
        title: data.meta_title,
        description: data.meta_description,
    };
}



const isMotor: boolean = false
const withDetails: boolean | undefined = true


export default async function CarPlateCheckPage() {
    const data: PageContentResponse = await getPageContent("car-plate-check")
    const { schema, description, faqs, title } = data
    return (
        <>
            {
                schema &&
                <ChildSchema
                    id="car-plate-check"
                    schema={schema}
                />
            }
            <InquiryComponent title={title || ""} isMotor={isMotor} withDetails={withDetails} />
            {
                description &&
                <ArticleSection description={description} />
            }
            {/* <CarPlateCheckAbout /> */}
            {
                faqs && 
                <Faq faqs={faqs} className="mt-10 mb-10" />
            }
        </>
    );
}
