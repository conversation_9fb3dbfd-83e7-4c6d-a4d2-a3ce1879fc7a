import Container from '@/components/common/Container';
import BackToHomeBtn from '@/components/PaymentPage/BackToHomeBtn';
import PaymentDetails from '@/components/PaymentPage/PaymentDetails';
import PaymentStatusHeader from '@/components/PaymentPage/PaymentStatusHeader';
import CustomButton from '@/components/UI/CustomButton'
import { apiClient } from '@/lib/apiClient';
import { ApiResponse, PaymentSummary } from '@/lib/types/qabzino-types';
import React from 'react';


type PageProps = {
  searchParams: Promise<{
    key?: string;
  }>
}


/**
 * Processes the API response into a simplified format for UI rendering
 * @param response The API response from the payment verification endpoint
 * @returns A simplified payment summary for UI components
 */
const processPaymentData = (response: ApiResponse): PaymentSummary => {
  // Check if we have valid bill data
  const hasData = Boolean(
    response?.data?.Parameters?.Bills &&
    response?.data?.Parameters.Bills.length > 0
  );

  if (!hasData) {
    return {
      isSuccess: false,
      hasData: false,
      totalAmount: 0,
      statusDescription: response?.data?.Status?.Description || "اطلاعات پرداخت یافت نشد",
      statusCode: response?.data?.Status?.Code || "ERROR"
    };
  }

  const bill = response.data.Parameters.Bills[0];
  const totalAmount = response.data.Parameters.TotalBillsAmount;

  return {
    isSuccess: Boolean(bill.Paid),
    bill,
    hasData: true,
    totalAmount,
    statusDescription: response.data.Status.Description,
    statusCode: response.data.Status.Code
  };
};



const Page = async ({ searchParams }: PageProps) => {
  const { key: callbackUrl } = await searchParams;

  // Fetch payment verification data
  const response = await apiClient("payment/qabzino/verfication", {
    method: "POST",
    body: { key: callbackUrl }
  }).then(res => res.json()) as ApiResponse;

  // Process the payment data
  const paymentData = processPaymentData(response);


  return (
    <Container className='!px-0'>
      <section className='md:w-[680px] max-md:!w-[96%] max-md:mx-auto'>
        <div className='max-w-md rounded-3xl bg-white p-3 md:my-32 my-3 mx-auto shadow-md max-md:mb-16 max-md:mt-8 px-4'>

          <PaymentStatusHeader
            isSuccess={paymentData.isSuccess}
          />


          {paymentData.hasData && paymentData.bill && (
            <PaymentDetails
              bill={paymentData.bill}
              totalAmount={paymentData.totalAmount}
            />
          )}


          <div className='w-1/2 mx-auto mt-5'>
            <BackToHomeBtn />
          </div>
        </div>
      </section>
    </Container>
  )
}

export default Page