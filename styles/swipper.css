.swiper-pagination-custom .swiper-pagination-bullet {
    background-color: black; /* Light white bullets */
    width: 5px;
    height: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    margin: 0 2px !important;
}

/* Active Pagination Bullet */
.swiper-pagination-custom .swiper-pagination-bullet-active {
    background-color: #F7BC06;
    border-radius: 5px;
    /*border: 1px solid #707070;*/
    width: 35px;
    height: 5px;
}

/* Blog Slider Specific Styles */
.blog-slider-container {
    overflow: visible !important;
}

.blog-slider-container .swiper-slide {
    transition: transform 0.3s ease;
    transform-origin: center;
}

/* Prevent overlap on Surface Pro and similar devices */
@media screen and (min-width: 1024px) and (max-width: 1440px) {
    .blog-slider-container .swiper-slide {
        margin: 0 8px;
    }

    .blog-slider-container .swiper-slide.swiper-slide-active {
        transform: scale(1.02) !important;
        z-index: 2;
    }
}

/* Surface Pro 7 specific (2736x1824 scaled to ~1368x912) */
@media screen and (min-width: 1366px) and (max-width: 1440px) {
    .blog-slider-container {
        padding: 0 20px;
    }

    .blog-slider-container .swiper-slide {
        margin: 0 12px;
        max-width: calc(25% - 24px);
    }
}

/* Larger tablets and small desktops */
@media screen and (min-width: 768px) and (max-width: 1024px) {
    .blog-slider-container .swiper-slide {
        margin: 0 6px;
    }

    .blog-slider-container .swiper-slide.swiper-slide-active {
        transform: scale(1.03) !important;
    }
}